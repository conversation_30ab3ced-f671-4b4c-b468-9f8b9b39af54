namespace OracleMS.Interfaces;

public interface IConfigurationService
{
    Task<T?> GetSettingAsync<T>(string key);
    Task SetSettingAsync<T>(string key, T value);
    Task<string> GetConnectionStringAsync(string connectionId);
    Task SetConnectionStringAsync(string connectionId, string connectionString);
    WindowSettings? GetWindowSettings();
    void SaveWindowSettings(WindowSettings settings);
}

public class WindowSettings
{
    public double? Left { get; set; }
    public double? Top { get; set; }
    public double? Width { get; set; }
    public double? Height { get; set; }
    public bool IsMaximized { get; set; }
    public string? DockingLayout { get; set; }
    public double? RightPanelWidth { get; set; }
}