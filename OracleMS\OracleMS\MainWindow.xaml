<Window x:Class="OracleMS.MainWindow"
        x:Name="MainWindowInstance"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:OracleMS"
        xmlns:views="clr-namespace:OracleMS.Views"
        mc:Ignorable="d"
        Title="Oracle Management Studio"
        Height="800"
        Width="1200"
        WindowState="Maximized">
    
    <Window.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
        <local:BooleanToVisibilityConverterWithParameter x:Key="BooleanToVisibilityConverterWithParameter"/>
        <local:StringEqualityToVisibilityConverter x:Key="StringEqualityToVisibilityConverter"/>
        
        <!-- Define styles for the application -->
        <Style x:Key="MenuItemStyle" TargetType="MenuItem">
            <Setter Property="Padding" Value="8,4"/>
        </Style>
        
        <Style x:Key="ToolBarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="4"/>
            <Setter Property="MinWidth" Value="24"/>
            <Setter Property="MinHeight" Value="24"/>
        </Style>
    </Window.Resources>

    <DockPanel>
        <!-- Menu Bar -->
        <Menu DockPanel.Dock="Top" Background="White" BorderBrush="LightGray" BorderThickness="0,0,0,1">
            <MenuItem Header="檔案(_F)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="新增連線(_N)" InputGestureText="Ctrl+N" Command="{Binding NewConnectionCommand}"/>
                <MenuItem Header="開啟查詢(_Q)" InputGestureText="Ctrl+Q" Command="{Binding NewQueryCommand}"/>
                <Separator/>
                <MenuItem Header="儲存(_S)" InputGestureText="Ctrl+S" Command="{Binding SaveCommand}"/>
                <MenuItem Header="另存新檔(_A)" InputGestureText="Ctrl+Shift+S" Command="{Binding SaveAsCommand}"/>
                <Separator/>
                <MenuItem Header="結束(_X)" InputGestureText="Alt+F4" Command="{Binding ExitCommand}"/>
            </MenuItem>
            
            <MenuItem Header="編輯(_E)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="復原(_U)" InputGestureText="Ctrl+Z"/>
                <MenuItem Header="重做(_R)" InputGestureText="Ctrl+Y"/>
                <Separator/>
                <MenuItem Header="剪下(_T)" InputGestureText="Ctrl+X"/>
                <MenuItem Header="複製(_C)" InputGestureText="Ctrl+C"/>
                <MenuItem Header="貼上(_P)" InputGestureText="Ctrl+V"/>
                <Separator/>
                <MenuItem Header="尋找(_F)" InputGestureText="Ctrl+F"/>
                <MenuItem Header="取代(_H)" InputGestureText="Ctrl+H"/>
            </MenuItem>
            
            <MenuItem Header="查詢(_Q)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="執行(_E)" InputGestureText="Ctrl+Enter" Command="{Binding ExecuteQueryCommand}"/>
                <MenuItem Header="執行選取的查詢(_S)" InputGestureText="F5" Command="{Binding ExecuteSelectedQueryCommand}"/>
                <MenuItem Header="停止執行(_T)" InputGestureText="Ctrl+Break" Command="{Binding StopExecutionCommand}"/>
                <Separator/>
                <MenuItem Header="顯示執行計畫(_P)" Command="{Binding ShowExecutionPlanCommand}"/>
            </MenuItem>
            
            <MenuItem Header="工具(_T)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="連線管理員(_C)" Command="{Binding ShowConnectionManagerCommand}"/>
                <MenuItem Header="物件瀏覽器(_O)" Command="{Binding ShowObjectExplorerCommand}"/>
                <MenuItem Header="腳本產生器(_S)" Command="{Binding ShowScriptGeneratorCommand}"/>
                <Separator/>
                <MenuItem Header="選項(_P)" Command="{Binding ShowOptionsCommand}"/>
            </MenuItem>
            
            <MenuItem Header="視窗(_W)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="重設視窗配置(_R)"/>
                <MenuItem Header="關閉所有標籤(_C)"/>
                <MenuItem Header="關閉其他標籤(_O)"/>
            </MenuItem>
            
            <MenuItem Header="說明(_H)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="關於 Oracle Management Studio(_A)"/>
                <MenuItem Header="使用說明(_H)" InputGestureText="F1"/>
            </MenuItem>
        </Menu>

        <!-- Tool Bar -->
        <ToolBarTray DockPanel.Dock="Top" Background="White">
            <ToolBar Background="Transparent" BorderBrush="LightGray" BorderThickness="0,0,0,1">
                <Button Style="{StaticResource ToolBarButtonStyle}" 
                        ToolTip="新增連線 (Ctrl+N)"
                        Command="{Binding NewConnectionCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔗" FontSize="16" Margin="0,0,4,0"/>
                        <TextBlock Text="新增連線"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource ToolBarButtonStyle}" 
                        ToolTip="新增查詢 (Ctrl+Q)"
                        Command="{Binding NewQueryCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📝" FontSize="16" Margin="0,0,4,0"/>
                        <TextBlock Text="新增查詢"/>
                    </StackPanel>
                </Button>
                
                <Separator Margin="4,0"/>
                
                <Button Style="{StaticResource ToolBarButtonStyle}" 
                        ToolTip="儲存 (Ctrl+S)"
                        Command="{Binding SaveCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,4,0"/>
                        <TextBlock Text="儲存"/>
                    </StackPanel>
                </Button>
                
                <Separator Margin="4,0"/>
                
                <Button Style="{StaticResource ToolBarButtonStyle}" 
                        ToolTip="執行查詢 (Ctrl+Enter)"
                        Command="{Binding ExecuteQueryCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="▶️" FontSize="16" Margin="0,0,4,0"/>
                        <TextBlock Text="執行"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource ToolBarButtonStyle}" 
                        ToolTip="停止執行 (Ctrl+Break)"
                        Command="{Binding StopExecutionCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⏹️" FontSize="16" Margin="0,0,4,0"/>
                        <TextBlock Text="停止"/>
                    </StackPanel>
                </Button>
                
                <Separator Margin="4,0"/>
                
                <Button Style="{StaticResource ToolBarButtonStyle}" 
                        ToolTip="連線管理員"
                        Command="{Binding ShowConnectionManagerCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔧" FontSize="16" Margin="0,0,4,0"/>
                        <TextBlock Text="連線管理"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource ToolBarButtonStyle}" 
                        ToolTip="物件瀏覽器"
                        Command="{Binding ShowObjectExplorerCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🗂️" FontSize="16" Margin="0,0,4,0"/>
                        <TextBlock Text="物件瀏覽器"/>
                    </StackPanel>
                </Button>
            </ToolBar>
        </ToolBarTray>

        <!-- Status Bar -->
        <StatusBar DockPanel.Dock="Bottom" Background="LightGray" BorderBrush="Gray" BorderThickness="0,1,0,0">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage, FallbackValue='就緒'}" Margin="4,2"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding ConnectionStatus, FallbackValue='未連線'}" Margin="4,2"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding CurrentTime, StringFormat='yyyy/MM/dd HH:mm:ss'}" Margin="4,2"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="Oracle Management Studio" Margin="4,2"/>
                    <TextBlock Text="|" Margin="4,2"/>
                    <TextBlock Text="v1.0" Margin="4,2"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>

        <!-- Main Content Area -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" MinWidth="300"/>
                <ColumnDefinition Width="3"/>
                <ColumnDefinition Width="220" MinWidth="20" MaxWidth="500"/>
            </Grid.ColumnDefinitions>

            <!-- Left Splitter -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="LightGray"/>

            <!-- Main Document Area -->
            <Grid Grid.Column="0" Background="White" Margin="2">
                <Border BorderBrush="Gray" BorderThickness="1">
                    <TabControl x:Name="DbTabControl"
                        Background="White"
                        BorderBrush="LightGray"
                        BorderThickness="1">

                        <!--<TabItem x:Name="DbTab">
                            <TabItem.Header>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="Session" VerticalAlignment="Center" Margin="0,0,8,0"/>                                    
                                </StackPanel>
                            </TabItem.Header>
                            <Grid Background="White">
                                <views:DbSession x:Name="dbSession"/>
                            </Grid>
                        </TabItem>-->
                    </TabControl>
                </Border>
            </Grid>

            <!-- Right Panel -->
            <Grid x:Name="RightPanel" Grid.Column="2" Background="White" Margin="2">
                <Border BorderBrush="Gray" BorderThickness="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*" MinHeight="100"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <Border Grid.Row="0" Background="LightGray" Padding="8,4">
                            <TextBlock Text="連線紀錄" FontWeight="SemiBold"/>
                        </Border>
                        
                        <Grid Grid.Row="1" Background="White">
                                <!-- Loading indicator -->
                                <StackPanel HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"
                                          Visibility="{Binding IsLoadingConnections, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="載入中..." 
                                             HorizontalAlignment="Center"
                                             Margin="0,0,0,10"
                                             FontWeight="SemiBold"
                                             Foreground="Gray"/>
                                    <ProgressBar IsIndeterminate="True" 
                                               Width="150" 
                                               Height="5"
                                               Background="Transparent"
                                               Foreground="#007ACC"/>
                                </StackPanel>
                                
                                <!-- Connection ListView -->
                                <ListView x:Name="ConnectionListView" 
                                         ItemsSource="{Binding SavedConnections}"
                                         BorderThickness="0"
                                         Background="Transparent"
                                         ScrollViewer.VerticalScrollBarVisibility="Auto"
                                         ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                         ScrollViewer.CanContentScroll="True"
                                         VirtualizingPanel.IsVirtualizing="True"
                                         VirtualizingPanel.VirtualizationMode="Recycling"
                                         VirtualizingPanel.ScrollUnit="Pixel"
                                         MouseDoubleClick="ConnectionListView_MouseDoubleClick"
                                         Visibility="{Binding IsLoadingConnections, Converter={StaticResource BooleanToVisibilityConverterWithParameter}, ConverterParameter=Inverse}">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <Grid.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="刪除連線"
                                                              Command="{Binding Path=DataContext.DeleteConnectionCommand, Source={x:Reference Name=MainWindowInstance}}"
                                                              CommandParameter="{Binding Id}">
                                                        <MenuItem.Icon>
                                                            <TextBlock Text="🗑️" FontSize="14"/>
                                                        </MenuItem.Icon>
                                                    </MenuItem>
                                                </ContextMenu>
                                            </Grid.ContextMenu>
                                            
                                            <Grid.ToolTip>
                                                <ToolTip>
                                                    <StackPanel>
                                                        <TextBlock Text="{Binding Name, StringFormat=名稱: {0}}" FontWeight="Bold" Margin="0,0,0,4"/>
                                                        <TextBlock Text="{Binding Server, StringFormat=伺服器: {0}}" />
                                                        <TextBlock Text="{Binding Port, StringFormat=埠號: {0}}" />
                                                        <TextBlock Text="{Binding ServiceName, StringFormat=服務名稱: {0}}" />
                                                        <TextBlock Text="{Binding Username, StringFormat=使用者名稱: {0}}" />
                                                        <TextBlock Text="{Binding Description, StringFormat=描述: {0}}" />
                                                        <TextBlock Text="{Binding LastConnected, StringFormat=最後連線時間: {0:yyyy/MM/dd HH:mm:ss}}" />
                                                    </StackPanel>
                                                </ToolTip>
                                            </Grid.ToolTip>
                                            
                                            <!-- Connection ID with loading indicator -->
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                
                                                <!-- Connection ID -->
                                                <TextBlock Grid.Column="0" 
                                                         Text="{Binding Id}" 
                                                         Padding="5,3" 
                                                         TextTrimming="CharacterEllipsis" />
                                                
                                                <!-- Loading indicator -->
                                                <StackPanel Grid.Column="1" 
                                                          Orientation="Horizontal" 
                                                          Visibility="{Binding DataContext.IsConnecting, 
                                                                     RelativeSource={RelativeSource AncestorType=ListView}, 
                                                                     Converter={StaticResource BooleanToVisibilityConverter}}">
                                                    <TextBlock Text="連線中..." 
                                                             Foreground="#007ACC" 
                                                             FontStyle="Italic"
                                                             Margin="5,0,5,0">
                                                        <TextBlock.Visibility>
                                                            <MultiBinding Converter="{StaticResource StringEqualityToVisibilityConverter}">
                                                                <Binding Path="Id" />
                                                                <Binding Path="DataContext.ConnectingConnectionId" RelativeSource="{RelativeSource AncestorType=ListView}" />
                                                            </MultiBinding>
                                                        </TextBlock.Visibility>
                                                    </TextBlock>
                                                </StackPanel>
                                            </Grid>
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem">
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                        <Setter Property="Padding" Value="8,4" />
                                        <Setter Property="Cursor" Value="Hand" />
                                        <Setter Property="BorderThickness" Value="0,0,0,1" />
                                        <Setter Property="BorderBrush" Value="#E5E5E5" />
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F5F5F5" />
                                                <Setter Property="BorderBrush" Value="#D0D0D0" />
                                                <Setter Property="FontWeight" Value="SemiBold" />
                                            </Trigger>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#E0E0E0" />
                                                <Setter Property="BorderBrush" Value="#C0C0C0" />
                                                <Setter Property="FontWeight" Value="Bold" />
                                            </Trigger>
                                            <MultiTrigger>
                                                <MultiTrigger.Conditions>
                                                    <Condition Property="IsMouseOver" Value="True" />
                                                    <Condition Property="IsSelected" Value="True" />
                                                </MultiTrigger.Conditions>
                                                <Setter Property="Background" Value="#D0D0D0" />
                                                <Setter Property="BorderBrush" Value="#B0B0B0" />
                                            </MultiTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ListView.ItemContainerStyle>
                                <ListView.Template>
                                    <ControlTemplate TargetType="ListView">
                                        <Border BorderThickness="{TemplateBinding BorderThickness}"
                                                BorderBrush="{TemplateBinding BorderBrush}">
                                            <ScrollViewer x:Name="ScrollViewer" 
                                                          Padding="{TemplateBinding Padding}"
                                                          Focusable="False"
                                                          VerticalScrollBarVisibility="Auto"
                                                          HorizontalScrollBarVisibility="Disabled"
                                                          Background="{TemplateBinding Background}">
                                                <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                            </ScrollViewer>
                                        </Border>
                                    </ControlTemplate>
                                </ListView.Template>
                                <!-- Empty state template -->
                                <ListView.Style>
                                    <Style TargetType="ListView">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding SavedConnections.Count}" Value="0">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate>
                                                            <StackPanel HorizontalAlignment="Center" 
                                                                      VerticalAlignment="Center"
                                                                      Margin="10">
                                                                <TextBlock Text="沒有可用的連線" 
                                                                         HorizontalAlignment="Center"
                                                                         FontWeight="SemiBold"
                                                                         Foreground="Gray"
                                                                         Margin="0,0,0,10"/>
                                                                <TextBlock Text="點擊「新增連線」按鈕或使用 Ctrl+N 建立新連線"
                                                                         HorizontalAlignment="Center"
                                                                         TextWrapping="Wrap"
                                                                         Foreground="Gray"/>
                                                                <Button Content="新增連線" 
                                                                      Command="{Binding NewConnectionCommand}"
                                                                      Margin="0,15,0,0"
                                                                      Padding="10,5"
                                                                      Background="#F0F0F0"
                                                                      BorderBrush="#D0D0D0">
                                                                    <Button.Style>
                                                                        <Style TargetType="Button">
                                                                            <Setter Property="Cursor" Value="Hand"/>
                                                                            <Style.Triggers>
                                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                                    <Setter Property="Background" Value="#E0E0E0"/>
                                                                                    <Setter Property="BorderBrush" Value="#C0C0C0"/>
                                                                                </Trigger>
                                                                            </Style.Triggers>
                                                                        </Style>
                                                                    </Button.Style>
                                                                </Button>
                                                            </StackPanel>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ListView.Style>
                            </ListView>
                        </Grid>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </DockPanel>
</Window>
