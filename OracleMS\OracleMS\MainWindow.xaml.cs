﻿using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using OracleMS.Models;
using OracleMS.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using OracleMS.Services;
using OracleMS.Interfaces;

namespace OracleMS
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IConfigurationService? _configurationService;
        private MainWindowViewModel? _viewModel;

        public MainWindow()
        {
            InitializeComponent();
            
            try
            {
                // For now, just initialize the window without complex dependencies
                Title = "Oracle Management Studio";
                
                // Try to get services if available
                if (App.ServiceProvider != null)
                {
                    _configurationService = App.ServiceProvider.GetService<IConfigurationService>();
                    _viewModel = App.ServiceProvider.GetService<MainWindowViewModel>();
                    
                    if (_viewModel != null)
                    {
                        DataContext = _viewModel;

                        // 訂閱資料庫連線建立事件
                        _viewModel.DatabaseConnectionEstablished += OnDatabaseConnectionEstablished;
                    }
                    
                    // Load window state if configuration service is available
                    if (_configurationService != null)
                    {
                        LoadWindowState();
                    }
                }
                
                // Handle window closing to save state
                Closing += MainWindow_Closing;
                
                // Set up keyboard shortcuts if ViewModel is available
                if (_viewModel != null)
                {
                    SetupKeyboardShortcuts();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error initializing main window: {ex.Message}\n\nStack trace:\n{ex.StackTrace}", "Error", 
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }



        private void SetupKeyboardShortcuts()
        {
            // Add keyboard shortcuts
            var newConnectionGesture = new KeyGesture(Key.N, ModifierKeys.Control);
            var newConnectionBinding = new KeyBinding(_viewModel?.NewConnectionCommand, newConnectionGesture);
            InputBindings.Add(newConnectionBinding);

            var newQueryGesture = new KeyGesture(Key.Q, ModifierKeys.Control);
            var newQueryBinding = new KeyBinding(_viewModel?.NewQueryCommand, newQueryGesture);
            InputBindings.Add(newQueryBinding);

            var saveGesture = new KeyGesture(Key.S, ModifierKeys.Control);
            var saveBinding = new KeyBinding(_viewModel?.SaveCommand, saveGesture);
            InputBindings.Add(saveBinding);

            var executeGesture = new KeyGesture(Key.Enter, ModifierKeys.Control);
            var executeBinding = new KeyBinding(_viewModel?.ExecuteQueryCommand, executeGesture);
            InputBindings.Add(executeBinding);

            var executeSelectedGesture = new KeyGesture(Key.F5);
            var executeSelectedBinding = new KeyBinding(_viewModel?.ExecuteSelectedQueryCommand, executeSelectedGesture);
            InputBindings.Add(executeSelectedBinding);

            var findGesture = new KeyGesture(Key.F, ModifierKeys.Control);
            var findBinding = new KeyBinding(_viewModel?.FindCommand, findGesture);
            InputBindings.Add(findBinding);

            var replaceGesture = new KeyGesture(Key.H, ModifierKeys.Control);
            var replaceBinding = new KeyBinding(_viewModel?.ReplaceCommand, replaceGesture);
            InputBindings.Add(replaceBinding);
        }

        private void LoadWindowState()
        {
            try
            {
                // Load window position and size from configuration
                var windowSettings = _configurationService.GetWindowSettings();
                
                if (windowSettings != null)
                {
                    // Restore window bounds
                    if (windowSettings.Left.HasValue && windowSettings.Top.HasValue)
                    {
                        Left = windowSettings.Left.Value;
                        Top = windowSettings.Top.Value;
                    }
                    
                    if (windowSettings.Width.HasValue && windowSettings.Height.HasValue)
                    {
                        Width = windowSettings.Width.Value;
                        Height = windowSettings.Height.Value;
                    }
                    
                    // Restore window state
                    if (windowSettings.IsMaximized)
                    {
                        WindowState = WindowState.Maximized;
                    }
                    
                    // 恢復右側面板寬度
                    if (windowSettings.RightPanelWidth.HasValue)
                    {
                        // 在UI完全加載後設置右側面板寬度
                        Dispatcher.BeginInvoke(new Action(() => 
                        {
                            SetRightPanelWidth(windowSettings.RightPanelWidth.Value);
                        }), System.Windows.Threading.DispatcherPriority.Loaded);
                    }
                    
                    // Note: Docking layout will be implemented later
                }
            }
            catch
            {
                // If loading fails, use default settings
            }
        }

        private void SaveWindowState()
        {
            try
            {
                // 獲取右側面板的當前寬度
                double rightPanelWidth = GetRightPanelWidth();
                
                var windowSettings = new WindowSettings
                {
                    Left = RestoreBounds.Left,
                    Top = RestoreBounds.Top,
                    Width = RestoreBounds.Width,
                    Height = RestoreBounds.Height,
                    IsMaximized = WindowState == WindowState.Maximized,
                    RightPanelWidth = rightPanelWidth > 0 ? rightPanelWidth : null
                };
                
                // Note: Docking layout will be implemented later
                
                _configurationService.SaveWindowSettings(windowSettings);
            }
            catch
            {
                // If saving fails, continue without error
            }
        }

        private void MainWindow_Closing(object? sender, CancelEventArgs e)
        {
            // Save window state before closing
            SaveWindowState();
            
            // Allow ViewModel to handle closing logic
            _viewModel?.OnWindowClosing(e);
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // Ensure window is visible on screen
            EnsureWindowIsOnScreen();
        }

        private void EnsureWindowIsOnScreen()
        {
            // Get the working area of the primary screen
            var workingArea = SystemParameters.WorkArea;
            
            // Ensure window is not positioned outside the screen
            if (Left < workingArea.Left)
                Left = workingArea.Left;
            if (Top < workingArea.Top)
                Top = workingArea.Top;
            if (Left + Width > workingArea.Right)
                Left = workingArea.Right - Width;
            if (Top + Height > workingArea.Bottom)
                Top = workingArea.Bottom - Height;
        }

        #region Event Handlers
        
        // 移除動態 ContextMenu 創建，改用 XAML 中的內建 ContextMenu
        // 這樣可以避免重複的確認對話框問題

        private void ConnectionListView_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // Get the clicked item
                var listView = (ListView)sender;
                var selectedItem = listView.SelectedItem as ConnectionInfo;
                
                if (selectedItem != null && _viewModel != null)
                {
                    // Check if password is missing
                    if (string.IsNullOrEmpty(selectedItem.Password))
                    {
                        // Password is missing, open ConnectionEditDialog with pre-filled information
                        _viewModel.StatusMessage = $"需要輸入密碼以連線到 {selectedItem.Id}";
                        
                        // Create connection manager view model
                        var connectionService = App.ServiceProvider?.GetService<IConnectionService>();
                        if (connectionService == null)
                        {
                            MessageBox.Show("無法取得連線服務", "系統錯誤",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                            return;
                        }
                        
                        var connectionManagerViewModel = new ConnectionManagerViewModel(connectionService);
                        
                        // Show connection edit dialog with pre-filled information
                        var dialog = new Views.ConnectionEditDialog(selectedItem, connectionManagerViewModel);
                        dialog.Owner = this;
                        
                        var dialogResult = dialog.ShowDialog();
                        
                        if (dialogResult == true && dialog.DatabaseConnection != null)
                        {
                            // Connection successful - the dialog has already created the connection
                            // Notify MainWindow about the new database connection
                            _viewModel.StatusMessage = $"已連線到 {selectedItem.Id}";
                            // Create a new database session tab directly
                            OnDatabaseConnectionEstablished(this, new ViewModels.DatabaseConnectionEventArgs(dialog.DatabaseConnection, selectedItem));
                        }
                        else
                        {
                            if (dialogResult == true)
                            {
                                _viewModel.StatusMessage = "連線建立失敗";
                            }
                            else
                            {
                                _viewModel.StatusMessage = "取消連線";
                            }
                        }
                        
                        return;
                    }
                    
                    // Show loading indicator
                    _viewModel.StatusMessage = $"正在連線到 {selectedItem.Id}...";
                    
                    // Attempt to establish connection using the ViewModel
                    _viewModel.CreateManagedConnectionAsync(selectedItem.Id)
                        .ContinueWith(task =>
                        {
                            // This will run on a background thread, so we need to dispatch back to UI thread
                            Dispatcher.Invoke(() =>
                            {
                                if (task.IsFaulted)
                                {
                                    // Get the actual exception
                                    var exception = task.Exception?.InnerException ?? task.Exception;
                                    string errorMessage = exception?.Message ?? "未知錯誤";
                                    string detailedMessage = string.Empty;
                                    
                                    // Provide more user-friendly error messages based on common connection issues
                                    if (errorMessage.Contains("ORA-12541") || errorMessage.Contains("host"))
                                    {
                                        detailedMessage = $"無法連線到伺服器 {selectedItem.Server}。請確認伺服器地址是否正確且可連線。";
                                    }
                                    else if (errorMessage.Contains("ORA-12514") || errorMessage.Contains("service"))
                                    {
                                        detailedMessage = $"找不到服務名稱 {selectedItem.ServiceName}。請確認服務名稱是否正確。";
                                    }
                                    else if (errorMessage.Contains("ORA-01017") || errorMessage.Contains("invalid username/password"))
                                    {
                                        detailedMessage = "使用者名稱或密碼錯誤。請確認登入資訊是否正確。";
                                        
                                        // Open ConnectionEditDialog for password error
                                        var connectionService = App.ServiceProvider?.GetService<IConnectionService>();
                                        if (connectionService != null)
                                        {
                                            var connectionManagerViewModel = new ConnectionManagerViewModel(connectionService);
                                            
                                            // Show connection edit dialog with pre-filled information
                                            var dialog = new Views.ConnectionEditDialog(selectedItem, connectionManagerViewModel);
                                            dialog.Owner = this;
                                            
                                            var dialogResult = dialog.ShowDialog();
                                            
                                            if (dialogResult == true && dialog.DatabaseConnection != null)
                                            {
                                                // Connection successful
                                                _viewModel.StatusMessage = $"已連線到 {selectedItem.Id}";
                                                // Create a new database session tab directly
                                                OnDatabaseConnectionEstablished(this, new ViewModels.DatabaseConnectionEventArgs(dialog.DatabaseConnection, selectedItem));
                                                return;
                                            }
                                        }
                                    }
                                    else if (errorMessage.Contains("timeout") || errorMessage.Contains("timed out"))
                                    {
                                        detailedMessage = $"連線逾時。請確認伺服器 {selectedItem.Server} 是否可連線，或增加連線逾時設定。";
                                    }
                                    else
                                    {
                                        detailedMessage = errorMessage;
                                    }
                                    
                                    // Prepare to show error dialog with detailed message
                                    MessageBox.Show(
                                        $"連線失敗: {selectedItem.Id}\n\n{detailedMessage}\n\n技術詳情: {errorMessage}", 
                                        "連線錯誤", 
                                        MessageBoxButton.OK, 
                                        MessageBoxImage.Error);
                                    
                                    // Update status message
                                    _viewModel.StatusMessage = $"連線失敗: {selectedItem.Id}";
                                }
                                // Connection successful - the OnDatabaseConnectionEstablished event will handle creating the tab
                                // The event is raised in CreateManagedConnectionAsync method
                            });
                        });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"處理連線時發生錯誤: {ex.Message}\n\n請確認連線設定是否正確。", 
                    "錯誤", 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Error);
                
                if (_viewModel != null)
                {
                    _viewModel.StatusMessage = "連線處理失敗";
                }
            }
        }

        private void OnOpenTableDataRequested(object? sender, OpenTableDataEventArgs e)
        {
            try
            {
                if (_viewModel != null && App.ServiceProvider != null)
                {
                    var databaseService = App.ServiceProvider.GetService<IDatabaseService>();
                    if (databaseService != null)
                    {
                        // Create a new DataGrid tab for the table
                        var dataGridViewModel = new DataGridViewModel(databaseService);
                        
                        // Get the active connection from connection service
                        var connectionService = App.ServiceProvider.GetService<IConnectionService>();
                        var activeConnections = connectionService?.GetActiveConnections().ToList();
                        
                        if (activeConnections?.Any() == true)
                        {
                            var connection = connectionService.GetActiveConnection(activeConnections.First().Id);
                            if (connection != null)
                            {
                                dataGridViewModel.SetConnection(connection);
                                dataGridViewModel.LoadTableData(e.TableName);
                            }
                        }

                        var tabItem = new TabItemViewModel
                        {
                            Header = $"資料: {e.TableName}",
                            Content = dataGridViewModel,
                            CanClose = true
                        };

                        _viewModel.AddTab(tabItem);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"開啟資料表資料失敗: {ex.Message}", "錯誤", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnOpenTableDesignRequested(object? sender, OpenTableDesignEventArgs e)
        {
            try
            {
                if (_viewModel != null && App.ServiceProvider != null)
                {
                    var databaseService = App.ServiceProvider.GetService<IDatabaseService>();
                    if (databaseService != null)
                    {
                        // Create a new TableDesigner tab for the table
                        var tableDesignerViewModel = new TableDesignerViewModel(databaseService);
                        
                        // Get the active connection from connection service
                        var connectionService = App.ServiceProvider.GetService<IConnectionService>();
                        var activeConnections = connectionService?.GetActiveConnections().ToList();
                        
                        if (activeConnections?.Any() == true)
                        {
                            var connection = connectionService.GetActiveConnection(activeConnections.First().Id);
                            if (connection != null)
                            {
                                tableDesignerViewModel.SetConnection(connection);
                                tableDesignerViewModel.LoadTableSchema(e.TableName);
                            }
                        }

                        var tabItem = new TabItemViewModel
                        {
                            Header = $"設計: {e.TableName}",
                            Content = tableDesignerViewModel,
                            CanClose = true
                        };

                        _viewModel.AddTab(tabItem);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"開啟資料表設計失敗: {ex.Message}", "錯誤", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnGenerateScriptRequested(object? sender, GenerateScriptEventArgs e)
        {
            try
            {
                if (_viewModel != null && App.ServiceProvider != null)
                {
                    var databaseService = App.ServiceProvider.GetService<IDatabaseService>();
                    if (databaseService != null)
                    {
                        // Create a new Query tab with the generated script
                        var transactionManager = App.ServiceProvider.GetService<ITransactionManager>();
                        if (transactionManager == null)
                            throw new InvalidOperationException("ITransactionManager service not registered");
                            
                        var queryEditorViewModel = new QueryEditorViewModel(databaseService, () => null); // TODO: 需要提供正確的連線獲取函數
                        
                        // Get the active connection from connection service
                        var connectionService = App.ServiceProvider.GetService<IConnectionService>();
                        var activeConnections = connectionService?.GetActiveConnections().ToList();
                        
                        if (activeConnections?.Any() == true)
                        {
                            // TODO: 需要修改為使用 DbSession 架構，而不是直接創建 QueryEditorViewModel
                            // var connection = connectionService.GetActiveConnection(activeConnections.First().Id);
                        }

                        // Set the generated script as the SQL text
                        queryEditorViewModel.SqlText = e.Script;

                        var tabItem = new TabItemViewModel
                        {
                            Header = e.Title,
                            Content = queryEditorViewModel,
                            CanClose = true
                        };

                        _viewModel.AddTab(tabItem);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"開啟產生的腳本失敗: {ex.Message}", "錯誤", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        // 處理資料庫連線建立事件
        private void OnDatabaseConnectionEstablished(object? sender, DatabaseConnectionEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("OnDatabaseConnectionEstablished event triggered");
                System.Diagnostics.Debug.WriteLine($"Connection: {e.Connection}");
                System.Diagnostics.Debug.WriteLine($"ConnectionInfo: {e.ConnectionInfo.Name}");

                // 新增一個 TabItem 到 DbTabControl，並創建新的 DbSession
                CreateNewDbSessionTab(e.Connection, e.ConnectionInfo);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating new DbSession tab: {ex.Message}");
                MessageBox.Show($"建立新的資料庫會話時發生錯誤: {ex.Message}", "錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 創建新的 DbSession TabItem
        private void CreateNewDbSessionTab(IDbConnection connection, ConnectionInfo connectionInfo)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Creating new DbSession tab");

                // 創建新的 TabItem
                var newTabItem = new TabItem();

                // 設置 Header 包含連線名稱和關閉按鈕
                var headerPanel = new StackPanel { Orientation = Orientation.Horizontal };

                var titleText = new TextBlock
                {
                    Text = connectionInfo.Id,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(0, 0, 8, 0)
                };

                var closeButton = new Button
                {
                    Content = "×",
                    Width = 16,
                    Height = 16,
                    FontSize = 10,
                    FontWeight = FontWeights.Bold,
                    Background = System.Windows.Media.Brushes.Transparent,
                    BorderBrush = Brushes.Transparent,
                    BorderThickness = new Thickness(1),
                    ToolTip = "關閉連線",
                    Margin = new Thickness(4, 0, 0, 0)
                };

                // 關閉按鈕的點擊事件
                closeButton.Click += (s, args) =>
                {
                    // 關閉連線
                    try
                    {
                        connection.Close();
                        connection?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error disposing connection: {ex.Message}");
                    }
                    //try
                    //{
                    //    var connectionService = App.ServiceProvider?.GetService<IConnectionService>();
                    //    connectionService?.CloseConnection(connectionInfo.Id);
                    //}
                    //catch (Exception ex)
                    //{
                    //    System.Diagnostics.Debug.WriteLine($"Error closing connection: {ex.Message}");
                    //}

                    // 移除 TabItem
                    DbTabControl.Items.Remove(newTabItem);
                };

                headerPanel.Children.Add(titleText);
                headerPanel.Children.Add(closeButton);
                newTabItem.Header = headerPanel;

                // 創建新的 DbSession
                var newDbSession = new Views.DbSession();
                newTabItem.Content = newDbSession;

                // 為新的 DbSession 創建並設定 ObjectExplorerViewModel
                // var databaseService = App.ServiceProvider?.GetService<IDatabaseService>();
                // var scriptGeneratorService = App.ServiceProvider?.GetService<IScriptGeneratorService>();

                // if (databaseService != null && scriptGeneratorService != null)
                // {
                //     var objectExplorerViewModel = new ObjectExplorerViewModel(databaseService, scriptGeneratorService);
                //     newDbSession.ObjectExplorerView.DataContext = objectExplorerViewModel;

                //     // 訂閱 ObjectExplorer 事件
                //     objectExplorerViewModel.OpenTableDataRequested += OnOpenTableDataRequested;
                //     objectExplorerViewModel.OpenTableDesignRequested += OnOpenTableDesignRequested;
                //     objectExplorerViewModel.GenerateScriptRequested += OnGenerateScriptRequested;

                //     System.Diagnostics.Debug.WriteLine("ObjectExplorerViewModel created and set for new DbSession");
                // }

                // 將連線傳遞給新的 DbSession
                System.Diagnostics.Debug.WriteLine("Setting connection to new DbSession");
                newDbSession.SetDatabaseConnection(connection, connectionInfo);
                System.Diagnostics.Debug.WriteLine("Connection set successfully to new DbSession");

                // 添加到 TabControl 並選中
                DbTabControl.Items.Add(newTabItem);
                DbTabControl.SelectedItem = newTabItem;

                System.Diagnostics.Debug.WriteLine($"New DbSession tab created for connection: {connectionInfo.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CreateNewDbSessionTab: {ex.Message}");
                throw;
            }
        }

        #endregion

        /// <summary>
        /// 動態設置右側面板的寬度
        /// </summary>
        /// <param name="width">新的寬度值（像素）</param>
        public void SetRightPanelWidth(double width)
        {
            try
            {
                // 獲取主網格的列定義
                var mainGrid = (Grid)((DockPanel)Content).Children[^1];
                var columnDefinitions = mainGrid.ColumnDefinitions;
                
                // 確保寬度在允許的範圍內
                double minWidth = columnDefinitions[2].MinWidth;
                double maxWidth = columnDefinitions[2].MaxWidth;
                
                if (width < minWidth)
                    width = minWidth;
                if (width > maxWidth)
                    width = maxWidth;
                
                // 設置右側面板的寬度
                columnDefinitions[2].Width = new GridLength(width);
                
                // 可選：更新配置設置以保存此寬度
                if (_configurationService != null)
                {
                    var windowSettings = _configurationService.GetWindowSettings() ?? new WindowSettings();
                    windowSettings.RightPanelWidth = width;
                    _configurationService.SaveWindowSettings(windowSettings);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"設置右側面板寬度時發生錯誤: {ex.Message}", "錯誤", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 獲取右側面板的當前寬度
        /// </summary>
        /// <returns>當前寬度（像素）</returns>
        public double GetRightPanelWidth()
        {
            try
            {
                // 獲取主網格的列定義
                var mainGrid = (Grid)((DockPanel)Content).Children[^1];
                var columnDefinitions = mainGrid.ColumnDefinitions;
                
                // 返回右側面板的當前寬度
                return columnDefinitions[2].ActualWidth;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"獲取右側面板寬度時發生錯誤: {ex.Message}", "錯誤", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return 0;
            }
        }
    }

    /// <summary>
    /// Converter to show welcome grid when no tabs are open
    /// </summary>
    public class CountToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int count)
            {
                return count == 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    /// <summary>
    /// Converter to toggle visibility based on boolean value with optional inversion
    /// </summary>
    public class BooleanToVisibilityConverterWithParameter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool boolValue = (bool)value;
            bool inverse = parameter != null && parameter.ToString() == "Inverse";
            
            if (inverse)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            else
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    /// <summary>
    /// Converter to toggle visibility based on string equality
    /// </summary>
    public class StringEqualityToVisibilityConverter : IValueConverter, IMultiValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
            {
                return Visibility.Collapsed;
            }
            
            string valueStr = value.ToString();
            string parameterStr = parameter.ToString();
            
            return valueStr == parameterStr ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
        
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            // For MultiBinding, we expect two values: the ID and the ConnectingConnectionId
            if (values == null || values.Length < 2 || values[0] == null || values[1] == null)
            {
                return Visibility.Collapsed;
            }
            
            string valueStr = values[0].ToString();
            string parameterStr = values[1].ToString();
            
            return valueStr == parameterStr ? Visibility.Visible : Visibility.Collapsed;
        }
        
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}