using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels;

public class QueryEditorViewModel : ViewModelBase, ISaveable, IDisposable
{
    private readonly IDatabaseService _databaseService;
    private readonly Func<IDbConnection?> _getConnection;
    private CancellationTokenSource? _cancellationTokenSource;
    private bool _isInitializing = true;

    private string _sqlText = string.Empty;
    public string SqlText
    {
        get => _sqlText;
        set
        {
            SetProperty(ref _sqlText, value);
            // 只有在非初始化狀態時才標記為已修改
            if (!_isInitializing)
            {
                HasUnsavedChanges = true;
            }
        }
    }

    private string _selectedText = string.Empty;
    public string SelectedText
    {
        get => _selectedText;
        set => SetProperty(ref _selectedText, value);
    }

    public ObservableCollection<QueryResult> Results { get; } = new();

    private string _messages = string.Empty;
    public string Messages
    {
        get => _messages;
        set => SetProperty(ref _messages, value);
    }

    private bool _isExecuting;
    public bool IsExecuting
    {
        get => _isExecuting;
        set 
        { 
            if (SetProperty(ref _isExecuting, value))
            {
                // Update transaction command availability when execution state changes
                ((AsyncRelayCommand)CommitTransactionCommand).NotifyCanExecuteChanged();
                ((AsyncRelayCommand)RollbackTransactionCommand).NotifyCanExecuteChanged();
            }
        }
    }

    private bool _hasUnsavedChanges;
    public bool HasUnsavedChanges
    {
        get => _hasUnsavedChanges;
        set => SetProperty(ref _hasUnsavedChanges, value);
    }

    // Pagination properties
    private int _defaultPageSize = 1000;
    public int DefaultPageSize
    {
        get => _defaultPageSize;
        set => SetProperty(ref _defaultPageSize, value);
    }

    private TimeSpan _lastExecutionTime;
    public TimeSpan LastExecutionTime
    {
        get => _lastExecutionTime;
        set => SetProperty(ref _lastExecutionTime, value);
    }

    private int _currentLine = 1;
    public int CurrentLine
    {
        get => _currentLine;
        set => SetProperty(ref _currentLine, value);
    }

    private int _currentColumn = 1;
    public int CurrentColumn
    {
        get => _currentColumn;
        set => SetProperty(ref _currentColumn, value);
    }

    private int _selectedTabIndex = 0;
    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set => SetProperty(ref _selectedTabIndex, value);
    }

    // Transaction state properties
    public bool HasActiveTransaction => _databaseService.HasActiveTransaction;
    public TransactionState TransactionState => _databaseService.CurrentTransactionState;
    
    private string _transactionStatusText = "無交易";
    public string TransactionStatusText
    {
        get => _transactionStatusText;
        private set => SetProperty(ref _transactionStatusText, value);
    }

    private string _filePath = string.Empty;
    public string FilePath
    {
        get => _filePath;
        set
        {
            SetProperty(ref _filePath, value);
            OnPropertyChanged(nameof(FileName));
        }
    }

    public string FileName => string.IsNullOrEmpty(FilePath) ? "未命名" : Path.GetFileName(FilePath);

    public ICommand ExecuteCommand { get; }
    public ICommand ExecuteSelectedCommand { get; }
    public ICommand CancelCommand { get; }
    public ICommand SaveCommand { get; }
    public ICommand SaveAsCommand { get; }
    public ICommand OpenCommand { get; }
    public ICommand NewCommand { get; }
    public ICommand ClearResultsCommand { get; }
    public ICommand ExportResultsCommand { get; }
    public ICommand ExportAllResultsCommand { get; }
    public ICommand FormatResultsCommand { get; }
    
    // Transaction control commands
    public ICommand CommitTransactionCommand { get; }
    public ICommand RollbackTransactionCommand { get; }

    // Pagination commands
    public ICommand NextPageCommand { get; }
    public ICommand LoadAllDataCommand { get; }
    public ICommand FirstPageCommand { get; }

    public QueryEditorViewModel(IDatabaseService databaseService, Func<IDbConnection?> getConnection)
    {
        _databaseService = databaseService;
        _getConnection = getConnection;

        ExecuteCommand = new AsyncRelayCommand(OnExecuteAsync, () => !IsExecuting && _getConnection() != null);
        ExecuteSelectedCommand = new AsyncRelayCommand(OnExecuteSelectedAsync, () => !IsExecuting && _getConnection() != null && !string.IsNullOrWhiteSpace(SelectedText));
        CancelCommand = new RelayCommand(OnCancel, () => IsExecuting);
        SaveCommand = new AsyncRelayCommand(OnSaveAsync);
        SaveAsCommand = new AsyncRelayCommand(OnSaveAsAsync);
        OpenCommand = new AsyncRelayCommand(OnOpenAsync);
        NewCommand = new RelayCommand(OnNew);
        ClearResultsCommand = new RelayCommand(OnClearResults);
        ExportResultsCommand = new AsyncRelayCommand<QueryResult>(OnExportResultsAsync);
        ExportAllResultsCommand = new AsyncRelayCommand(OnExportAllResultsAsync, () => Results.Any(r => r.Data != null));
        FormatResultsCommand = new RelayCommand(OnFormatResults);
        
        // Transaction control commands with enhanced availability logic
        CommitTransactionCommand = new AsyncRelayCommand(OnCommitTransactionAsync, CanExecuteTransactionCommand);
        RollbackTransactionCommand = new AsyncRelayCommand(OnRollbackTransactionAsync, CanExecuteTransactionCommand);

        // Pagination commands
        NextPageCommand = new AsyncRelayCommand<QueryResult>(OnNextPageAsync, CanExecuteNextPage);
        LoadAllDataCommand = new AsyncRelayCommand<QueryResult>(OnLoadAllDataAsync, CanExecuteLoadAllData);
        FirstPageCommand = new AsyncRelayCommand<QueryResult>(OnFirstPageAsync, CanExecuteFirstPage);

        // Subscribe to transaction state changes
        _databaseService.TransactionStateChanged += OnTransactionStateChanged;
        
        // Initialize transaction status text
        UpdateTransactionStatusText();

        // 初始化完成，開始追蹤變更
        _isInitializing = false;
    }

    public IDbConnection? GetConnection()
    {
        return _getConnection();
    }

    private async Task OnExecuteAsync()
    {
        if (string.IsNullOrWhiteSpace(SqlText) || _getConnection() == null) return;

        await ExecuteSqlAsync(SqlText);
    }

    private async Task OnExecuteSelectedAsync()
    {
        if (string.IsNullOrWhiteSpace(SelectedText) || _getConnection() == null) return;

        await ExecuteSqlAsync(SelectedText);
    }

    private async Task ExecuteSqlAsync(string sql)
    {
        IsExecuting = true;
        _cancellationTokenSource = new CancellationTokenSource();

        try
        {
            // 查詢開始前清空結果
            Results.Clear();

            AddMessage($"開始執行查詢: {DateTime.Now:HH:mm:ss}");
            AddMessage($"SQL: {sql.Trim()}");

            // Split SQL into individual statements
            var statements = SplitSqlStatements(sql);
            
            // Use transaction-aware database service method
            var results = await ExecuteStatementsWithCancellationAsync(statements);
            
            // Process results - only add SELECT results to Results collection
            foreach (var result in results)
            {
                // Only add SELECT queries to Results collection
                if (result.QueryType == Models.QueryType.Select && result.IsSuccess)
                {
                    // Convert to paged result if it's a SELECT query
                    var pagedResult = await ConvertToPagedResultAsync(result);
                    Results.Add(pagedResult);
                }

                AddMessage(result.GetSummary());

                // Add transaction-related messages
                if (result.TransactionStarted)
                {
                    AddMessage("自動啟動交易");
                }
                if (result.TransactionCommitted)
                {
                    AddMessage("DDL 語句執行後自動提交交易");
                }

                if (!result.IsSuccess)
                {
                    AddMessage($"錯誤詳情: {result.ErrorMessage}");
                    break; // Stop on first error
                }
            }

            // Calculate total execution time
            var totalTime = results.Sum(r => r.ExecutionTime.TotalMilliseconds);
            LastExecutionTime = TimeSpan.FromMilliseconds(totalTime);
            AddMessage($"所有查詢執行完成，總耗時: {LastExecutionTime.TotalSeconds:F2} 秒");

            // 查詢結束後將 ResultsTabControl 的 SelectedIndex 設為 0
            if (Results.Count > 0)
            {
                SelectedTabIndex = 0;
            }
        }
        catch (OperationCanceledException)
        {
            AddMessage("查詢已取消");
        }
        catch (Exception ex)
        {
            AddMessage($"查詢執行失敗: {ex.Message}");
            
            // Provide specific guidance for transaction-related errors
            if (ex.Message.Contains("transaction") || ex.Message.Contains("交易"))
            {
                if (ex.Message.Contains("start") || ex.Message.Contains("啟動"))
                {
                    AddMessage("交易啟動失敗 - 請檢查資料庫連線狀態");
                    AddMessage("提示: 您可以嘗試重新執行或檢查資料庫權限");
                }
                else if (ex.Message.Contains("connection") || ex.Message.Contains("連線"))
                {
                    AddMessage("資料庫連線問題 - 建議重新連線後再試");
                }
            }
            
            // Add error result
            var errorResult = QueryResult.Failure(ex.Message, ex, sql);
            Results.Add(errorResult);
        }
        finally
        {
            IsExecuting = false;
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
        }
    }

    private async Task<IEnumerable<QueryResult>> ExecuteStatementsWithCancellationAsync(IEnumerable<string> statements)
    {
        var connection = _getConnection();
        if (connection == null)
            return new List<QueryResult>();

        try
        {
            // Use transaction-aware database service method for multiple statements
            var cancellationToken = _cancellationTokenSource?.Token ?? CancellationToken.None;
            return await _databaseService.ExecuteMultipleSqlWithTransactionAsync(
                connection,
                statements.Where(s => !string.IsNullOrWhiteSpace(s)).Select(s => s.Trim()),
                cancellationToken);
        }
        catch (OperationCanceledException)
        {
            var cancelResult = QueryResult.Failure("批次執行已取消", new OperationCanceledException(), 
                string.Join("; ", statements));
            return new List<QueryResult> { cancelResult };
        }
        catch (Exception ex)
        {
            var errorResult = QueryResult.Failure($"批次執行失敗: {ex.Message}", ex, 
                string.Join("; ", statements));
            return new List<QueryResult> { errorResult };
        }
    }

    private async Task<QueryResult> ExecuteSingleStatementWithCancellationAsync(string sql)
    {
        var connection = _getConnection();
        if (connection == null)
            return QueryResult.Failure("資料庫連線未建立", null, sql);

        try
        {
            // Use transaction-aware database service method with cancellation token
            var cancellationToken = _cancellationTokenSource?.Token ?? CancellationToken.None;

            return await _databaseService.ExecuteSqlWithTransactionAsync(connection, sql, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            return QueryResult.Failure("查詢已取消", new OperationCanceledException(), sql);
        }
        catch (Exception ex)
        {
            return QueryResult.Failure(ex.Message, ex, sql);
        }
    }

    private void OnCancel()
    {
        _cancellationTokenSource?.Cancel();
        AddMessage("正在取消查詢...");
    }

    private async Task OnCommitTransactionAsync()
    {
        // Handle gracefully when no transaction is active (Requirement 2.4)
        if (!HasActiveTransaction)
        {
            AddMessage("沒有活動交易可以提交");
            return;
        }

        try
        {
            AddMessage("正在提交交易...");
            await _databaseService.CommitTransactionAsync();
            AddMessage("交易已成功提交");
        }
        catch (Exception ex)
        {
            // Display error and maintain transaction state (Requirement 5.2)
            AddMessage($"提交交易失敗: {ex.Message}");
            
            // Log additional details for debugging
            if (ex.InnerException != null)
            {
                AddMessage($"內部錯誤: {ex.InnerException.Message}");
            }
            
            // Provide specific guidance based on error type
            if (ex.Message.Contains("connection") || ex.Message.Contains("連線"))
            {
                AddMessage("提示: 資料庫連線問題，請檢查連線狀態或重新連線");
            }
            else if (ex.Message.Contains("timeout") || ex.Message.Contains("逾時"))
            {
                AddMessage("提示: 操作逾時，您可以嘗試重新提交或選擇回滾交易");
            }
            else
            {
                AddMessage("提示: 您可以嘗試重新提交或選擇回滾交易");
            }
            
            // Check if transaction state is still active for retry possibility
            if (HasActiveTransaction)
            {
                AddMessage("交易仍處於活動狀態，可以重試提交或選擇回滾");
            }
        }
    }

    private async Task OnRollbackTransactionAsync()
    {
        // Handle gracefully when no transaction is active (Requirement 2.4)
        if (!HasActiveTransaction)
        {
            AddMessage("沒有活動交易可以回滾");
            return;
        }

        try
        {
            AddMessage("正在回滾交易...");
            await _databaseService.RollbackTransactionAsync();
            AddMessage("交易已成功回滾");
        }
        catch (Exception ex)
        {
            // Display error and attempt to clean up transaction state (Requirement 5.3)
            AddMessage($"回滾交易失敗: {ex.Message}");
            
            // Log additional details for debugging
            if (ex.InnerException != null)
            {
                AddMessage($"內部錯誤: {ex.InnerException.Message}");
            }
            
            // Provide specific guidance based on error type
            if (ex.Message.Contains("connection") || ex.Message.Contains("連線"))
            {
                AddMessage("連線已中斷，交易狀態已自動清理");
            }
            else if (ex.Message.Contains("timeout") || ex.Message.Contains("逾時"))
            {
                AddMessage("回滾操作逾時，但交易狀態已清理");
            }
            else
            {
                AddMessage("回滾失敗，但交易狀態已清理");
            }
            
            // The TransactionManager should have already cleaned up, but verify
            if (HasActiveTransaction)
            {
                // If transaction is still active, attempt manual cleanup
                try
                {
                    AddMessage("嘗試手動清理交易狀態...");
                    // Note: Transaction state is managed by DatabaseService
                    AddMessage("交易狀態已手動重置");
                }
                catch (Exception resetEx)
                {
                    AddMessage($"手動清理交易狀態失敗: {resetEx.Message}");
                    AddMessage("建議: 請重新連線資料庫以確保狀態一致性");
                }
            }
        }
    }

    private void OnTransactionStateChanged(object? sender, TransactionStateChangedEventArgs e)
    {
        // Update UI properties when transaction state changes
        OnPropertyChanged(nameof(HasActiveTransaction));
        OnPropertyChanged(nameof(TransactionState));
        UpdateTransactionStatusText();
        
        // Update command availability
        ((AsyncRelayCommand)CommitTransactionCommand).NotifyCanExecuteChanged();
        ((AsyncRelayCommand)RollbackTransactionCommand).NotifyCanExecuteChanged();
        
        // Add message about state change
        if (!string.IsNullOrEmpty(e.Message))
        {
            AddMessage(e.Message);
        }
        
        if (e.Error != null)
        {
            AddMessage($"交易錯誤: {e.Error.Message}");
        }
    }

    private bool CanExecuteTransactionCommand()
    {
        // Commands are available when there's an active transaction and not currently executing queries
        return HasActiveTransaction && !IsExecuting && _getConnection() != null;
    }

    private void UpdateTransactionStatusText()
    {
        TransactionStatusText = TransactionState switch
        {
            Interfaces.TransactionState.None => "無交易",
            Interfaces.TransactionState.Active => "交易進行中",
            Interfaces.TransactionState.Committed => "交易已提交",
            Interfaces.TransactionState.RolledBack => "交易已回滾",
            Interfaces.TransactionState.Error => "交易錯誤",
            _ => "未知狀態"
        };
    }

    private async Task OnSaveAsync()
    {
        if (string.IsNullOrEmpty(FilePath))
        {
            await OnSaveAsAsync();
            return;
        }

        try
        {
            await File.WriteAllTextAsync(FilePath, SqlText);
            HasUnsavedChanges = false;
            AddMessage($"檔案已儲存: {FilePath}");
        }
        catch (Exception ex)
        {
            AddMessage($"儲存檔案失敗: {ex.Message}");
        }
    }

    private async Task OnSaveAsAsync()
    {
        // In a real implementation, this would show a SaveFileDialog
        // For now, we'll use a default path
        var defaultPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
            $"Query_{DateTime.Now:yyyyMMdd_HHmmss}.sql");

        try
        {
            await File.WriteAllTextAsync(defaultPath, SqlText);
            FilePath = defaultPath;
            HasUnsavedChanges = false;
            AddMessage($"檔案已另存為: {defaultPath}");
        }
        catch (Exception ex)
        {
            AddMessage($"另存檔案失敗: {ex.Message}");
        }
    }

    private async Task OnOpenAsync()
    {
        // In a real implementation, this would show an OpenFileDialog
        // For now, we'll just add a message
        AddMessage("開啟檔案功能需要在 UI 層實作");
        await Task.CompletedTask;
    }

    // 新增查詢標籤頁的事件
    public event EventHandler? NewQueryTabRequested;

    private void OnNew()
    {
        // 觸發事件通知需要新增查詢標籤頁
        NewQueryTabRequested?.Invoke(this, EventArgs.Empty);
        //AddMessage("已請求新增查詢標籤頁");
    }

    private void OnClearResults()
    {
        Results.Clear();
        AddMessage("查詢結果已清除");
    }

    private async Task OnExportResultsAsync(QueryResult? result)
    {
        if (result?.Data == null) return;

        try
        {
            var exportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                $"QueryResult_{DateTime.Now:yyyyMMdd_HHmmss}.csv");

            await ExportToCsvAsync(result.Data, exportPath);
            AddMessage($"結果已匯出至: {exportPath}");
        }
        catch (Exception ex)
        {
            AddMessage($"匯出失敗: {ex.Message}");
        }
    }

    private async Task OnExportAllResultsAsync()
    {
        var resultsWithData = Results.Where(r => r.Data != null).ToList();
        if (!resultsWithData.Any()) return;

        try
        {
            var exportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                $"AllQueryResults_{DateTime.Now:yyyyMMdd_HHmmss}");

            // Export each result to a separate file
            for (int i = 0; i < resultsWithData.Count; i++)
            {
                var result = resultsWithData[i];
                var fileName = $"{exportPath}_Result{i + 1}.csv";
                await ExportToCsvAsync(result.Data!, fileName);
            }

            // Create a summary file
            var summaryPath = $"{exportPath}_Summary.txt";
            await CreateExportSummaryAsync(resultsWithData, summaryPath);

            AddMessage($"所有結果已匯出至: {Path.GetDirectoryName(exportPath)}");
        }
        catch (Exception ex)
        {
            AddMessage($"批次匯出失敗: {ex.Message}");
        }
    }

    private async Task CreateExportSummaryAsync(List<QueryResult> results, string summaryPath)
    {
        using var writer = new StreamWriter(summaryPath);
        
        await writer.WriteLineAsync("查詢結果匯出摘要");
        await writer.WriteLineAsync($"匯出時間: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        await writer.WriteLineAsync($"總結果數: {results.Count}");
        await writer.WriteLineAsync();

        for (int i = 0; i < results.Count; i++)
        {
            var result = results[i];
            await writer.WriteLineAsync($"結果 {i + 1}:");
            await writer.WriteLineAsync($"  SQL: {result.SqlStatement}");
            await writer.WriteLineAsync($"  執行時間: {result.ExecutionTime.TotalMilliseconds:F0} 毫秒");
            await writer.WriteLineAsync($"  資料筆數: {result.RowsAffected}");
            await writer.WriteLineAsync($"  執行狀態: {(result.IsSuccess ? "成功" : "失敗")}");
            if (!result.IsSuccess)
                await writer.WriteLineAsync($"  錯誤訊息: {result.ErrorMessage}");
            await writer.WriteLineAsync();
        }
    }

    private void OnFormatResults()
    {
        // Format the results display for better readability
        var formattedResults = new List<string>();
        
        foreach (var result in Results)
        {
            if (result.Data != null)
            {
                formattedResults.Add(FormatDataTableAsText(result.Data));
            }
            else
            {
                formattedResults.Add($"執行結果: {result.GetSummary()}");
            }
        }

        if (formattedResults.Any())
        {
            var formatted = string.Join("\n" + new string('-', 80) + "\n", formattedResults);
            AddMessage("結果格式化完成");
            AddMessage($"格式化結果:\n{formatted}");
        }
    }

    private string FormatDataTableAsText(DataTable dataTable)
    {
        if (dataTable.Rows.Count == 0)
            return "無資料";

        var result = new System.Text.StringBuilder();
        
        // Calculate column widths
        var columnWidths = new int[dataTable.Columns.Count];
        for (int i = 0; i < dataTable.Columns.Count; i++)
        {
            columnWidths[i] = Math.Max(dataTable.Columns[i].ColumnName.Length, 10);
            foreach (DataRow row in dataTable.Rows)
            {
                var cellValue = row[i]?.ToString() ?? "";
                columnWidths[i] = Math.Max(columnWidths[i], cellValue.Length);
            }
            // Limit column width to prevent extremely wide output
            columnWidths[i] = Math.Min(columnWidths[i], 50);
        }

        // Header
        for (int i = 0; i < dataTable.Columns.Count; i++)
        {
            result.Append(dataTable.Columns[i].ColumnName.PadRight(columnWidths[i] + 2));
        }
        result.AppendLine();

        // Separator
        for (int i = 0; i < dataTable.Columns.Count; i++)
        {
            result.Append(new string('-', columnWidths[i] + 2));
        }
        result.AppendLine();

        // Data rows (limit to first 100 rows for performance)
        var rowCount = Math.Min(dataTable.Rows.Count, 100);
        for (int rowIndex = 0; rowIndex < rowCount; rowIndex++)
        {
            var row = dataTable.Rows[rowIndex];
            for (int i = 0; i < dataTable.Columns.Count; i++)
            {
                var cellValue = row[i]?.ToString() ?? "";
                if (cellValue.Length > columnWidths[i])
                    cellValue = cellValue.Substring(0, columnWidths[i] - 3) + "...";
                result.Append(cellValue.PadRight(columnWidths[i] + 2));
            }
            result.AppendLine();
        }

        if (dataTable.Rows.Count > 100)
        {
            result.AppendLine($"... 還有 {dataTable.Rows.Count - 100} 筆資料未顯示");
        }

        return result.ToString();
    }

    private async Task ExportToCsvAsync(DataTable dataTable, string filePath)
    {
        using var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8);
        
        // Write headers
        var headers = dataTable.Columns.Cast<DataColumn>().Select(column => EscapeCsvField(column.ColumnName));
        await writer.WriteLineAsync(string.Join(",", headers));

        // Write data
        foreach (DataRow row in dataTable.Rows)
        {
            var values = row.ItemArray.Select(field => EscapeCsvField(field?.ToString() ?? ""));
            await writer.WriteLineAsync(string.Join(",", values));
        }
    }

    private static string EscapeCsvField(string field)
    {
        if (string.IsNullOrEmpty(field))
            return "\"\"";

        // Escape quotes and wrap in quotes if necessary
        if (field.Contains("\"") || field.Contains(",") || field.Contains("\n") || field.Contains("\r"))
        {
            return "\"" + field.Replace("\"", "\"\"") + "\"";
        }

        return field;
    }

    private void AddMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        Messages += $"[{timestamp}] {message}\n";
    }

    private static bool IsSelectStatement(string sql)
    {
        var trimmed = sql.Trim().ToUpper();
        return trimmed.StartsWith("SELECT") || trimmed.StartsWith("WITH");
    }

    /// <summary>
    /// 將普通查詢結果轉換為分頁結果
    /// </summary>
    /// <param name="result">原始查詢結果</param>
    /// <returns>分頁查詢結果</returns>
    private async Task<QueryResult> ConvertToPagedResultAsync(QueryResult result)
    {
        if (result.QueryType != Models.QueryType.Select || !result.IsSuccess)
            return result;

        var connection = _getConnection();
        if (connection == null)
            return result;

        try
        {
            // 使用分頁服務重新執行查詢
            var pagedResult = await _databaseService.ExecutePagedSelectAsync(
                connection,
                result.SqlStatement,
                DefaultPageSize,
                1, // 第一頁
                false, // 不載入全部資料
                null,
                CancellationToken.None);

            return pagedResult;
        }
        catch (Exception ex)
        {
            AddMessage($"轉換為分頁結果失敗: {ex.Message}");
            // 如果分頁轉換失敗，返回原始結果
            return result;
        }
    }

    private static List<string> SplitSqlStatements(string sql)
    {
        // Simple SQL statement splitter - in a real implementation, this would be more sophisticated
        return sql.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries)
                  .Select(s => s.Trim())
                  .Where(s => !string.IsNullOrEmpty(s))
                  .ToList();
    }

    public void Save()
    {
        _ = OnSaveAsync();
    }

    public void SetCursorPosition(int line, int column)
    {
        CurrentLine = line;
        CurrentColumn = column;
    }

    public void LoadFromFile(string filePath)
    {
        try
        {
            _isInitializing = true; // 暫時停止追蹤變更
            SqlText = File.ReadAllText(filePath);
            FilePath = filePath;
            HasUnsavedChanges = false;
            _isInitializing = false; // 重新開始追蹤變更
            AddMessage($"檔案已載入: {filePath}");
        }
        catch (Exception ex)
        {
            AddMessage($"載入檔案失敗: {ex.Message}");
        }
    }

    private bool _disposed = false;

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Unsubscribe from transaction state changes
                _databaseService.TransactionStateChanged -= OnTransactionStateChanged;
                
                // Cancel any ongoing operations
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
            }
            
            _disposed = true;
        }
    }

    #region Pagination Methods

    /// <summary>
    /// 載入下一頁資料
    /// </summary>
    /// <param name="result">當前查詢結果</param>
    private async Task OnNextPageAsync(QueryResult? result)
    {
        if (result == null || !result.IsPagedResult || !result.HasMoreData || result.IsFullDataLoaded)
            return;

        var connection = _getConnection();
        if (connection == null)
            return;

        try
        {
            IsExecuting = true;
            AddMessage($"正在載入第 {result.CurrentPage + 1} 頁...");

            var nextPageResult = await _databaseService.ExecutePagedSelectAsync(
                connection,
                result.SelectStatement,
                result.PageSize,
                result.CurrentPage + 1,
                false, // 不載入全部資料
                result.Parameters,
                _cancellationTokenSource?.Token ?? CancellationToken.None);

            if (nextPageResult.IsSuccess)
            {
                // 更新當前結果
                var index = Results.IndexOf(result);
                if (index >= 0)
                {
                    Results[index] = nextPageResult;
                    AddMessage(nextPageResult.GetSummary());
                }
            }
            else
            {
                AddMessage($"載入下一頁失敗: {nextPageResult.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            AddMessage($"載入下一頁時發生錯誤: {ex.Message}");
        }
        finally
        {
            IsExecuting = false;
        }
    }

    /// <summary>
    /// 載入全部資料
    /// </summary>
    /// <param name="result">當前查詢結果</param>
    private async Task OnLoadAllDataAsync(QueryResult? result)
    {
        if (result == null || !result.IsPagedResult || result.IsFullDataLoaded)
            return;

        var connection = _getConnection();
        if (connection == null)
            return;

        try
        {
            IsExecuting = true;
            AddMessage("正在載入全部資料...");

            var allDataResult = await _databaseService.ExecutePagedSelectAsync(
                connection,
                result.SelectStatement,
                result.PageSize,
                1,
                true, // 載入全部資料
                result.Parameters,
                _cancellationTokenSource?.Token ?? CancellationToken.None);

            if (allDataResult.IsSuccess)
            {
                // 更新當前結果
                var index = Results.IndexOf(result);
                if (index >= 0)
                {
                    Results[index] = allDataResult;
                    AddMessage(allDataResult.GetSummary());
                }
            }
            else
            {
                AddMessage($"載入全部資料失敗: {allDataResult.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            AddMessage($"載入全部資料時發生錯誤: {ex.Message}");
        }
        finally
        {
            IsExecuting = false;
        }
    }

    /// <summary>
    /// 跳回第一頁
    /// </summary>
    /// <param name="result">當前查詢結果</param>
    private async Task OnFirstPageAsync(QueryResult? result)
    {
        if (result == null || !result.IsPagedResult || result.CurrentPage == 1)
            return;

        var connection = _getConnection();
        if (connection == null)
            return;

        try
        {
            IsExecuting = true;
            AddMessage("正在跳回第一頁...");

            var firstPageResult = await _databaseService.ExecutePagedSelectAsync(
                connection,
                result.SelectStatement,
                result.PageSize,
                1,
                false, // 不載入全部資料
                result.Parameters,
                _cancellationTokenSource?.Token ?? CancellationToken.None);

            if (firstPageResult.IsSuccess)
            {
                // 更新當前結果
                var index = Results.IndexOf(result);
                if (index >= 0)
                {
                    Results[index] = firstPageResult;
                    AddMessage(firstPageResult.GetSummary());
                }
            }
            else
            {
                AddMessage($"跳回第一頁失敗: {firstPageResult.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            AddMessage($"跳回第一頁時發生錯誤: {ex.Message}");
        }
        finally
        {
            IsExecuting = false;
        }
    }

    /// <summary>
    /// 檢查是否可以載入下一頁
    /// </summary>
    /// <param name="result">查詢結果</param>
    /// <returns>是否可以載入下一頁</returns>
    private bool CanExecuteNextPage(QueryResult? result)
    {
        return !IsExecuting && result != null && result.IsPagedResult && result.HasMoreData && !result.IsFullDataLoaded;
    }

    /// <summary>
    /// 檢查是否可以載入全部資料
    /// </summary>
    /// <param name="result">查詢結果</param>
    /// <returns>是否可以載入全部資料</returns>
    private bool CanExecuteLoadAllData(QueryResult? result)
    {
        return !IsExecuting && result != null && result.IsPagedResult && !result.IsFullDataLoaded;
    }

    /// <summary>
    /// 檢查是否可以跳回第一頁
    /// </summary>
    /// <param name="result">查詢結果</param>
    /// <returns>是否可以跳回第一頁</returns>
    private bool CanExecuteFirstPage(QueryResult? result)
    {
        return !IsExecuting && result != null && result.IsPagedResult && result.CurrentPage > 1;
    }

    #endregion
}