using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.Services;
using OracleMS.ViewModels;
using Xunit;

namespace OracleMS.Tests
{
    public class MainWindowTests
    {
        private readonly Mock<IConnectionService> _mockConnectionService;
        private readonly Mock<IDatabaseService> _mockDatabaseService;
        private readonly Mock<IScriptGeneratorService> _mockScriptGeneratorService;
        private readonly Mock<IConfigurationService> _mockConfigurationService;
        private readonly MainWindowViewModel _viewModel;

        public MainWindowTests()
        {
            _mockConnectionService = new Mock<IConnectionService>();
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
            _mockConfigurationService = new Mock<IConfigurationService>();

            _viewModel = new MainWindowViewModel(
                _mockConnectionService.Object,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object);
        }

        [Fact]
        public async Task ConnectionListView_MouseDoubleClick_ShouldEstablishConnection()
        {
            // Note: This test would normally use UI automation to test the actual double-click event
            // Since that requires a running UI thread, we'll test the underlying method that gets called
            // when the double-click event occurs
            
            // Arrange
            var connectionId = "test-connection";
            var connectionInfo = new ConnectionInfo 
            { 
                Id = connectionId, 
                Name = "Test Connection",
                Server = "localhost",
                Port = 1521,
                ServiceName = "test",
                Username = "user",
                Password = "pass"
            };
            
            // Add the connection to the SavedConnections collection
            _viewModel.SavedConnections.Add(connectionInfo);
            
            // Mock the database connection
            var mockDbConnection = new Mock<IDbConnection>();
            
            _mockConnectionService
                .Setup(cs => cs.CreateManagedConnectionAsync(It.Is<ConnectionInfo>(c => c.Id == connectionId)))
                .ReturnsAsync(mockDbConnection.Object);

            // Track if the event was raised
            bool eventRaised = false;
            ConnectionInfo? eventConnectionInfo = null;
            IDbConnection? eventConnection = null;
            
            _viewModel.DatabaseConnectionEstablished += (sender, args) => 
            {
                eventRaised = true;
                eventConnectionInfo = args.ConnectionInfo;
                eventConnection = args.Connection;
            };

            // Act - Simulate what happens when the double-click event handler is called
            await _viewModel.CreateManagedConnectionAsync(connectionId);

            // Assert
            Assert.True(eventRaised);
            Assert.Same(connectionInfo, eventConnectionInfo);
            Assert.Same(mockDbConnection.Object, eventConnection);
            
            // Verify the connection service was called
            _mockConnectionService.Verify(cs => cs.CreateManagedConnectionAsync(It.Is<ConnectionInfo>(c => c.Id == connectionId)), Times.Once);
        }

        [Fact]
        public async Task ConnectionListView_MouseDoubleClick_ShouldHandleConnectionFailure()
        {
            // Arrange
            var connectionId = "test-connection";
            var connectionInfo = new ConnectionInfo 
            { 
                Id = connectionId, 
                Name = "Test Connection",
                Server = "localhost",
                Port = 1521,
                ServiceName = "test",
                Username = "user",
                Password = "pass"
            };
            
            // Add the connection to the SavedConnections collection
            _viewModel.SavedConnections.Add(connectionInfo);
            
            // Setup the connection service to throw an exception
            _mockConnectionService
                .Setup(cs => cs.CreateManagedConnectionAsync(It.Is<ConnectionInfo>(c => c.Id == connectionId)))
                .ThrowsAsync(new Exception("Connection failed"));

            // Act & Assert - Simulate what happens when the double-click event handler is called
            await Assert.ThrowsAsync<Exception>(() => _viewModel.CreateManagedConnectionAsync(connectionId));
            
            // Verify the state is reset
            Assert.False(_viewModel.IsConnecting);
            Assert.Null(_viewModel.ConnectingConnectionId);
        }

        [Fact]
        public void DatabaseConnectionEstablished_ShouldCreateNewDbSession()
        {
            // This test would normally verify that a new DbSession tab is created when the
            // DatabaseConnectionEstablished event is raised
            // Since this requires UI testing, we'll focus on verifying that the event is raised
            // with the correct parameters, which we've already tested in the previous test
            
            // The actual tab creation would be tested in an integration test with a running UI
        }
    }
}