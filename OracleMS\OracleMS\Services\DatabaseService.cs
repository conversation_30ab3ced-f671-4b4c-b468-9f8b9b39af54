using System.Data;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using Oracle.ManagedDataAccess.Client;
using OracleMS.Exceptions;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.Services;

/// <summary>
/// 資料庫服務，提供高階的資料庫操作功能
/// </summary>
public class DatabaseService : IDatabaseService, IDisposable
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILogger<DatabaseService> _logger;
    private readonly ITransactionManager _transactionManager;

    public DatabaseService(
        IDatabaseRepository databaseRepository,
        ILogger<DatabaseService> logger)
    {
        _databaseRepository = databaseRepository ?? throw new ArgumentNullException(nameof(databaseRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 創建內建的 TransactionManager
        _transactionManager = new TransactionManager();
    }

    // 公開交易管理功能
    public bool HasActiveTransaction => _transactionManager.HasActiveTransaction;
    public TransactionState CurrentTransactionState => _transactionManager.CurrentState;
    public event EventHandler<TransactionStateChangedEventArgs>? TransactionStateChanged
    {
        add => _transactionManager.StateChanged += value;
        remove => _transactionManager.StateChanged -= value;
    }

    public async Task BeginTransactionAsync(IDbConnection connection)
    {
        await _transactionManager.BeginTransactionAsync(connection);
    }

    public async Task CommitTransactionAsync()
    {
        await _transactionManager.CommitAsync();
    }

    public async Task RollbackTransactionAsync()
    {
        await _transactionManager.RollbackAsync();
    }

    /// <summary>
    /// 取得資料庫物件清單
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="type">物件類型</param>
    /// <returns>資料庫物件清單</returns>
    public async Task<IEnumerable<DatabaseObject>> GetDatabaseObjectsAsync(IDbConnection connection, DatabaseObjectType type)
    {
        try
        {
            _logger.LogInformation("正在取得資料庫物件清單，類型: {ObjectType}", type);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            var stopwatch = Stopwatch.StartNew();
            var objects = await _databaseRepository.GetDatabaseObjectsAsync(connection, type);
            stopwatch.Stop();

            var objectList = objects.ToList();
            _logger.LogInformation("成功取得 {Count} 個 {ObjectType} 物件，耗時 {ElapsedMs} 毫秒", 
                objectList.Count, type, stopwatch.ElapsedMilliseconds);

            return objectList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取得資料庫物件清單失敗，類型: {ObjectType}", type);
            throw new OracleManagementException($"取得資料庫物件清單失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 執行查詢並返回結果
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 查詢語句</param>
    /// <returns>查詢結果</returns>
    public async Task<DataTable> ExecuteQueryAsync(IDbConnection connection, string sql)
    {
        try
        {
            _logger.LogInformation("正在執行查詢，SQL 長度: {SqlLength}", sql?.Length ?? 0);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(sql))
                throw new ArgumentException("SQL 語句不能為空", nameof(sql));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            // 檢查是否為查詢語句
            var trimmedSql = sql.Trim().ToUpperInvariant();
            if (!IsSelectQuery(trimmedSql))
            {
                throw new ArgumentException("此方法僅支援 SELECT 查詢語句");
            }

            var stopwatch = Stopwatch.StartNew();
            var result = await _databaseRepository.ExecuteQueryAsync(connection, sql);
            stopwatch.Stop();

            _logger.LogInformation("查詢執行成功，返回 {RowCount} 筆資料，耗時 {ElapsedMs} 毫秒",
                result.Rows.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (OracleException ex)
        {
            _logger.LogError(ex, "Oracle 查詢執行失敗，錯誤代碼: {ErrorCode}", ex.Number);
            var userMessage = GetUserFriendlyOracleErrorMessage(ex);
            throw new OracleManagementException($"查詢執行失敗: {userMessage}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查詢執行失敗");
            throw new OracleManagementException($"查詢執行失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 執行非查詢 SQL 語句
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 語句</param>
    /// <returns>受影響的資料列數</returns>
    public async Task<int> ExecuteNonQueryAsync(IDbConnection connection, string sql)
    {
        try
        {
            _logger.LogInformation("正在執行非查詢語句，SQL 長度: {SqlLength}", sql?.Length ?? 0);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(sql))
                throw new ArgumentException("SQL 語句不能為空", nameof(sql));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            // 檢查是否為非查詢語句
            var trimmedSql = sql.Trim().ToUpperInvariant();
            if (IsSelectQuery(trimmedSql))
            {
                throw new ArgumentException("此方法不支援 SELECT 查詢語句，請使用 ExecuteQueryAsync");
            }

            var stopwatch = Stopwatch.StartNew();
            var rowsAffected = await _databaseRepository.ExecuteNonQueryAsync(connection, sql);
            stopwatch.Stop();

            _logger.LogInformation("非查詢語句執行成功，影響 {RowsAffected} 筆資料，耗時 {ElapsedMs} 毫秒", 
                rowsAffected, stopwatch.ElapsedMilliseconds);

            return rowsAffected;
        }
        catch (OracleException ex)
        {
            _logger.LogError(ex, "Oracle 非查詢語句執行失敗，錯誤代碼: {ErrorCode}", ex.Number);
            var userMessage = GetUserFriendlyOracleErrorMessage(ex);
            throw new OracleManagementException($"語句執行失敗: {userMessage}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "非查詢語句執行失敗");
            throw new OracleManagementException($"語句執行失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 執行 SQL 語句並返回單一值
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 語句</param>
    /// <returns>查詢結果</returns>
    public async Task<QueryResult> ExecuteScalarAsync(IDbConnection connection, string sql)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("正在執行標量查詢，SQL 長度: {SqlLength}", sql?.Length ?? 0);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(sql))
                throw new ArgumentException("SQL 語句不能為空", nameof(sql));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandTimeout = 300; // 5 分鐘逾時

            var result = await Task.Run(() => command.ExecuteScalar());
            stopwatch.Stop();

            _logger.LogInformation("標量查詢執行成功，耗時 {ElapsedMs} 毫秒", stopwatch.ElapsedMilliseconds);

            // 創建包含標量結果的 DataTable
            var dataTable = new DataTable();
            dataTable.Columns.Add("Result", typeof(object));
            var row = dataTable.NewRow();
            row["Result"] = result ?? DBNull.Value;
            dataTable.Rows.Add(row);

            return QueryResult.Success(dataTable, stopwatch.Elapsed, sql);
        }
        catch (OracleException ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Oracle 標量查詢執行失敗，錯誤代碼: {ErrorCode}", ex.Number);
            var userMessage = GetUserFriendlyOracleErrorMessage(ex);
            return QueryResult.Failure($"標量查詢執行失敗: {userMessage}", ex, sql, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "標量查詢執行失敗");
            return QueryResult.Failure($"標量查詢執行失敗: {ex.Message}", ex, sql, stopwatch.Elapsed);
        }
    }

    /// <summary>
    /// 取得資料表結構資訊
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="tableName">資料表名稱</param>
    /// <returns>資料表結構資訊</returns>
    public async Task<TableSchema> GetTableSchemaAsync(IDbConnection connection, string tableName)
    {
        try
        {
            _logger.LogInformation("正在取得資料表結構: {TableName}", tableName);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(tableName))
                throw new ArgumentException("資料表名稱不能為空", nameof(tableName));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            var stopwatch = Stopwatch.StartNew();
            var schema = await _databaseRepository.GetTableSchemaAsync(connection, tableName);
            stopwatch.Stop();

            _logger.LogInformation("成功取得資料表結構: {TableName}，包含 {ColumnCount} 個欄位，耗時 {ElapsedMs} 毫秒", 
                tableName, schema.Columns.Count, stopwatch.ElapsedMilliseconds);

            return schema;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取得資料表結構失敗: {TableName}", tableName);
            throw new OracleManagementException($"取得資料表結構失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 執行 SQL 語句並返回完整的查詢結果物件
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 語句</param>
    /// <param name="cancellationToken">取消權杖</param>
    /// <returns>查詢結果物件</returns>
    public async Task<QueryResult> ExecuteSqlAsync(IDbConnection connection, string sql, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("正在執行 SQL 語句，長度: {SqlLength}", sql?.Length ?? 0);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(sql))
                throw new ArgumentException("SQL 語句不能為空", nameof(sql));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            // Check for cancellation before execution
            cancellationToken.ThrowIfCancellationRequested();

            var trimmedSql = sql.Trim();
            
            if (IsSelectQuery(trimmedSql.ToUpperInvariant()))
            {
                // 執行查詢
                var dataTable = await ExecuteQueryWithCancellationAsync(connection, sql, cancellationToken);
                stopwatch.Stop();
                
                return QueryResult.Success(dataTable, stopwatch.Elapsed, sql);
            }
            else
            {
                // 執行非查詢
                var rowsAffected = await ExecuteNonQueryWithCancellationAsync(connection, sql, cancellationToken);
                stopwatch.Stop();
                
                return QueryResult.Success(rowsAffected, stopwatch.Elapsed, sql);
            }
        }
        catch (OperationCanceledException)
        {
            stopwatch.Stop();
            _logger.LogInformation("SQL 語句執行已取消");
            return QueryResult.Failure("查詢已取消", new OperationCanceledException(), sql ?? string.Empty, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "SQL 語句執行失敗");
            
            var errorMessage = ex is OracleManagementException ? ex.Message : $"SQL 執行失敗: {ex.Message}";
            return QueryResult.Failure(errorMessage, ex, sql ?? string.Empty, stopwatch.Elapsed);
        }
    }

    /// <summary>
    /// 執行查詢並返回結果（支援取消）
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 查詢語句</param>
    /// <param name="cancellationToken">取消權杖</param>
    /// <returns>查詢結果</returns>
    private async Task<DataTable> ExecuteQueryWithCancellationAsync(IDbConnection connection, string sql, CancellationToken cancellationToken)
    {
        try
        {
            // Create a task that can be cancelled
            var queryTask = ExecuteQueryAsync(connection, sql);
            var cancellationTask = Task.Delay(Timeout.Infinite, cancellationToken);

            var completedTask = await Task.WhenAny(queryTask, cancellationTask);

            if (completedTask == cancellationTask)
            {
                cancellationToken.ThrowIfCancellationRequested();
            }

            return await queryTask;
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("查詢執行已取消");
            throw;
        }
    }

    /// <summary>
    /// 執行查詢並返回結果（支援取消和交易）
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 查詢語句</param>
    /// <param name="transaction">資料庫交易（可為 null）</param>
    /// <param name="cancellationToken">取消權杖</param>
    /// <returns>查詢結果</returns>
    private async Task<DataTable> ExecuteQueryWithCancellationAndTransactionAsync(
        IDbConnection connection,
        string sql,
        IDbTransaction? transaction,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("正在執行查詢（支援交易），SQL 長度: {SqlLength}", sql?.Length ?? 0);

            // 直接創建 command，不使用 repository，以便支援交易
            using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandTimeout = 300; // 5 分鐘逾時

            // 關鍵：如果有交易，指派給 command
            if (transaction != null)
            {
                command.Transaction = transaction;
            }

            var dataTable = new DataTable();

            // Create a task that can be cancelled
            var queryTask = Task.Run(() =>
            {
                if (command is OracleCommand oracleCommand)
                {
                    using var adapter = new OracleDataAdapter(oracleCommand);
                    adapter.Fill(dataTable);
                }
                else
                {
                    using var reader = command.ExecuteReader();
                    dataTable.Load(reader);
                }
                return dataTable;
            }, cancellationToken);

            var cancellationTask = Task.Delay(Timeout.Infinite, cancellationToken);

            var completedTask = await Task.WhenAny(queryTask, cancellationTask);

            if (completedTask == cancellationTask)
            {
                cancellationToken.ThrowIfCancellationRequested();
            }

            var result = await queryTask;
            _logger.LogInformation("查詢執行成功（支援交易），返回 {RowCount} 筆資料", result.Rows.Count);
            return result;
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("查詢執行已取消（支援交易）");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查詢執行失敗（支援交易）");
            throw;
        }
    }

    /// <summary>
    /// 執行非查詢 SQL 語句（支援取消）
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 語句</param>
    /// <param name="cancellationToken">取消權杖</param>
    /// <returns>受影響的資料列數</returns>
    private async Task<int> ExecuteNonQueryWithCancellationAsync(IDbConnection connection, string sql, CancellationToken cancellationToken)
    {
        try
        {
            // Create a task that can be cancelled
            var executeTask = ExecuteNonQueryAsync(connection, sql);
            var cancellationTask = Task.Delay(Timeout.Infinite, cancellationToken);
            
            var completedTask = await Task.WhenAny(executeTask, cancellationTask);
            
            if (completedTask == cancellationTask)
            {
                cancellationToken.ThrowIfCancellationRequested();
            }
            
            return await executeTask;
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("非查詢語句執行已取消");
            throw;
        }
    }

    /// <summary>
    /// 執行多個 SQL 語句
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sqlStatements">SQL 語句清單</param>
    /// <param name="useTransaction">是否使用交易</param>
    /// <returns>查詢結果清單</returns>
    public async Task<IEnumerable<QueryResult>> ExecuteMultipleSqlAsync(
        IDbConnection connection, 
        IEnumerable<string> sqlStatements, 
        bool useTransaction = true)
    {
        var results = new List<QueryResult>();
        var statements = sqlStatements.ToList();
        
        _logger.LogInformation("正在執行 {Count} 個 SQL 語句，使用交易: {UseTransaction}", 
            statements.Count, useTransaction);

        if (!useTransaction)
        {
            // 不使用交易，逐一執行
            foreach (var sql in statements)
            {
                var result = await ExecuteSqlAsync(connection, sql);
                results.Add(result);
                
                if (!result.IsSuccess)
                {
                    _logger.LogWarning("SQL 語句執行失敗，停止後續執行: {ErrorMessage}", result.ErrorMessage);
                    break;
                }
            }
            
            return results;
        }

        // 使用交易執行
        IDbTransaction? transaction = null;
        try
        {
            transaction = connection.BeginTransaction();
            _logger.LogDebug("開始資料庫交易");

            foreach (var sql in statements)
            {
                var result = await ExecuteSqlAsync(connection, sql);
                results.Add(result);
                
                if (!result.IsSuccess)
                {
                    _logger.LogWarning("SQL 語句執行失敗，回復交易: {ErrorMessage}", result.ErrorMessage);
                    transaction.Rollback();
                    return results;
                }
            }

            transaction.Commit();
            _logger.LogInformation("所有 SQL 語句執行成功，交易已提交");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "執行多個 SQL 語句時發生錯誤");
            
            try
            {
                transaction?.Rollback();
                _logger.LogInformation("交易已回復");
            }
            catch (Exception rollbackEx)
            {
                _logger.LogError(rollbackEx, "回復交易時發生錯誤");
            }
            
            var errorResult = QueryResult.Failure($"批次執行失敗: {ex.Message}", ex, string.Join("; ", statements));
            results.Add(errorResult);
        }
        finally
        {
            transaction?.Dispose();
        }

        return results;
    }

    /// <summary>
    /// 取得資料表資料（支援分頁）
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="tableName">資料表名稱</param>
    /// <param name="pageNumber">頁碼（從 1 開始）</param>
    /// <param name="pageSize">每頁資料筆數</param>
    /// <param name="whereClause">WHERE 條件（可選）</param>
    /// <param name="orderBy">排序條件（可選）</param>
    /// <returns>分頁查詢結果</returns>
    public async Task<QueryResult> GetTableDataAsync(
        IDbConnection connection, 
        string tableName, 
        int pageNumber = 1, 
        int pageSize = 1000,
        string? whereClause = null,
        string? orderBy = null)
    {
        try
        {
            _logger.LogInformation("正在取得資料表資料: {TableName}，頁碼: {PageNumber}，每頁: {PageSize}", 
                tableName, pageNumber, pageSize);

            if (string.IsNullOrWhiteSpace(tableName))
                throw new ArgumentException("資料表名稱不能為空", nameof(tableName));

            if (pageNumber < 1)
                throw new ArgumentException("頁碼必須大於 0", nameof(pageNumber));

            if (pageSize < 1 || pageSize > 10000)
                throw new ArgumentException("每頁資料筆數必須在 1-10000 範圍內", nameof(pageSize));

            // 建構分頁查詢 SQL
            var sql = BuildPagedQuery(tableName, pageNumber, pageSize, whereClause, orderBy);
            
            return await ExecuteSqlAsync(connection, sql);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取得資料表資料失敗: {TableName}", tableName);
            throw new OracleManagementException($"取得資料表資料失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 檢查是否為 SELECT 查詢語句
    /// </summary>
    /// <param name="sql">SQL 語句（已轉為大寫）</param>
    /// <returns>是否為查詢語句</returns>
    private static bool IsSelectQuery(string sql)
    {
        return sql.StartsWith("SELECT") || sql.StartsWith("WITH");
    }

    /// <summary>
    /// 執行分頁 SELECT 查詢
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="selectSql">SELECT SQL 語句</param>
    /// <param name="pageSize">每頁資料筆數</param>
    /// <param name="pageNumber">頁碼（從 1 開始）</param>
    /// <param name="loadAllData">是否載入全部資料</param>
    /// <param name="parameters">查詢參數</param>
    /// <param name="cancellationToken">取消權杖</param>
    /// <returns>分頁查詢結果</returns>
    public async Task<QueryResult> ExecutePagedSelectAsync(
        IDbConnection connection,
        string selectSql,
        int pageSize = 1000,
        int pageNumber = 1,
        bool loadAllData = false,
        Dictionary<string, object>? parameters = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("正在執行{QueryType}查詢，頁碼: {PageNumber}，每頁: {PageSize}",
                loadAllData ? "全部資料" : "分頁", pageNumber, pageSize);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(selectSql))
                throw new ArgumentException("SQL 語句不能為空", nameof(selectSql));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            if (pageSize < 1 || pageSize > 10000)
                throw new ArgumentException("每頁資料筆數必須在 1-10000 範圍內", nameof(pageSize));

            if (pageNumber < 1)
                throw new ArgumentException("頁碼必須大於 0", nameof(pageNumber));

            var trimmedSql = selectSql.Trim().ToUpperInvariant();
            if (!IsSelectQuery(trimmedSql))
                throw new ArgumentException("此方法僅支援 SELECT 查詢語句");

            cancellationToken.ThrowIfCancellationRequested();

            DataTable dataTable;
            bool hasMoreData = false;

            if (loadAllData)
            {
                // 載入全部資料
                dataTable = await ExecuteQueryWithCancellationAsync(connection, selectSql, cancellationToken);
            }
            else
            {
                // 建構分頁查詢，多取一筆來判斷是否還有下一頁
                var pagedSql = BuildPagedSelectQuery(selectSql, pageSize + 1, pageNumber);
                dataTable = await ExecuteQueryWithCancellationAsync(connection, pagedSql, cancellationToken);

                // 如果返回的資料筆數等於 pageSize + 1，表示還有下一頁
                hasMoreData = dataTable.Rows.Count > pageSize;

                // 移除多取的那一筆資料
                if (hasMoreData && dataTable.Rows.Count > pageSize)
                {
                    dataTable.Rows.RemoveAt(dataTable.Rows.Count - 1);
                }
            }

            stopwatch.Stop();

            _logger.LogInformation("{QueryType}查詢執行成功，返回 {RowCount} 筆資料，耗時 {ElapsedMs} 毫秒",
                loadAllData ? "全部資料" : "分頁", dataTable.Rows.Count, stopwatch.ElapsedMilliseconds);

            return QueryResult.SuccessWithPagination(
                dataTable,
                stopwatch.Elapsed,
                selectSql,
                pageSize,
                pageNumber,
                hasMoreData,
                loadAllData,
                parameters);
        }
        catch (OperationCanceledException)
        {
            stopwatch.Stop();
            _logger.LogInformation("查詢已取消");
            return QueryResult.Failure("查詢已取消", new OperationCanceledException(), selectSql, stopwatch.Elapsed);
        }
        catch (OracleException ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Oracle 查詢執行失敗，錯誤代碼: {ErrorCode}", ex.Number);
            var userMessage = GetUserFriendlyOracleErrorMessage(ex);
            return QueryResult.Failure($"查詢失敗: {userMessage}", ex, selectSql, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "查詢執行失敗");
            return QueryResult.Failure($"查詢失敗: {ex.Message}", ex, selectSql, stopwatch.Elapsed);
        }
    }



    /// <summary>
    /// 建構分頁 SELECT 查詢
    /// </summary>
    /// <param name="selectSql">原始 SELECT 語句</param>
    /// <param name="pageSize">每頁資料筆數</param>
    /// <param name="pageNumber">頁碼（從 1 開始）</param>
    /// <returns>分頁查詢 SQL</returns>
    private static string BuildPagedSelectQuery(string selectSql, int pageSize, int pageNumber)
    {
        var offset = (pageNumber - 1) * pageSize;

        // 移除可能的分號
        var cleanSql = selectSql.TrimEnd(';', ' ', '\t', '\r', '\n');

        // 使用 Oracle 的 ROWNUM 分頁語法
        return $@"
            SELECT * FROM (
                SELECT a.*, ROWNUM rnum FROM (
                    {cleanSql}
                ) a
                WHERE ROWNUM <= {offset + pageSize}
            )
            WHERE rnum > {offset}";
    }

    /// <summary>
    /// 建構分頁查詢 SQL
    /// </summary>
    /// <param name="tableName">資料表名稱</param>
    /// <param name="pageNumber">頁碼</param>
    /// <param name="pageSize">每頁資料筆數</param>
    /// <param name="whereClause">WHERE 條件</param>
    /// <param name="orderBy">排序條件</param>
    /// <returns>分頁查詢 SQL</returns>
    private static string BuildPagedQuery(string tableName, int pageNumber, int pageSize, 
        string? whereClause, string? orderBy)
    {
        var offset = (pageNumber - 1) * pageSize;
        var whereCondition = string.IsNullOrWhiteSpace(whereClause) ? "" : $"WHERE {whereClause}";
        var orderByClause = string.IsNullOrWhiteSpace(orderBy) ? "ORDER BY ROWID" : $"ORDER BY {orderBy}";

        return $@"
            SELECT * FROM (
                SELECT a.*, ROWNUM rnum FROM (
                    SELECT * FROM {tableName}
                    {whereCondition}
                    {orderByClause}
                ) a
                WHERE ROWNUM <= {offset + pageSize}
            )
            WHERE rnum > {offset}";
    }

    /// <summary>
    /// 執行 SQL 語句並返回完整的查詢結果物件（支援交易管理）
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 語句</param>
    /// <param name="cancellationToken">取消權杖</param>
    /// <returns>查詢結果物件</returns>
    public async Task<QueryResult> ExecuteSqlWithTransactionAsync(
        IDbConnection connection,
        string sql,
        CancellationToken cancellationToken = default,
        bool skipTransactionManagement = false)
    {
        var stopwatch = Stopwatch.StartNew();
        bool transactionStarted = false;
        bool transactionCommitted = false;
        bool transactionRolledBack = false;
        
        try
        {
            _logger.LogInformation("正在執行 SQL 語句（支援交易管理），長度: {SqlLength}", sql?.Length ?? 0);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(sql))
                throw new ArgumentException("SQL 語句不能為空", nameof(sql));

            // 使用內建的 TransactionManager

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            // Check for cancellation before execution
            cancellationToken.ThrowIfCancellationRequested();

            var trimmedSql = sql.Trim();
            var isSelectQuery = IsSelectQuery(trimmedSql.ToUpperInvariant());
            
            // Start transaction for non-query statements if not already active
            // Skip transaction management if we're in a batch execution context
            if (!skipTransactionManagement && !isSelectQuery && !_transactionManager.HasActiveTransaction)
            {
                await _transactionManager.BeginTransactionAsync(connection);
                transactionStarted = true;
                _logger.LogDebug(_transactionManager.HasActiveTransaction.ToString());
                _logger.LogDebug("自動啟動交易用於非查詢語句");
            }

            QueryResult result;
            
            if (isSelectQuery)
            {
                // 執行查詢 - 確保在正確的交易上下文中執行
                var dataTable = await ExecuteQueryWithTransactionAsync(connection, sql, cancellationToken);
                stopwatch.Stop();

                result = QueryResult.Success(dataTable, stopwatch.Elapsed, sql,
                    transactionStarted, transactionCommitted, transactionRolledBack,
                    _transactionManager.CurrentState);
            }
            else
            {
                // 執行非查詢 - 確保在正確的交易上下文中執行
                var rowsAffected = await ExecuteNonQueryWithTransactionAsync(connection, sql, cancellationToken);

                // Check for auto-commit after execution
                if (await _transactionManager.AutoCommitIfNeededAsync(sql))
                {
                    transactionCommitted = true;
                    _logger.LogDebug("DDL 語句執行後自動提交交易");
                }

                stopwatch.Stop();

                result = QueryResult.Success(rowsAffected, stopwatch.Elapsed, sql,
                    transactionStarted, transactionCommitted, transactionRolledBack,
                    _transactionManager.CurrentState);
            }

            _logger.LogInformation("SQL 語句執行成功（交易管理），耗時 {ElapsedMs} 毫秒", stopwatch.ElapsedMilliseconds);
            return result;
        }
        catch (OperationCanceledException)
        {
            stopwatch.Stop();
            _logger.LogInformation("SQL 語句執行已取消");
            return QueryResult.Failure("查詢已取消", new OperationCanceledException(), sql ?? string.Empty, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "SQL 語句執行失敗（交易管理）");
            
            var errorMessage = ex is OracleManagementException ? ex.Message : $"SQL 執行失敗: {ex.Message}";
            return QueryResult.Failure(errorMessage, ex, sql ?? string.Empty, stopwatch.Elapsed);
        }
    }

    /// <summary>
    /// 執行多個 SQL 語句（支援交易管理）
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sqlStatements">SQL 語句清單</param>
    /// <param name="cancellationToken">取消權杖</param>
    /// <returns>查詢結果清單</returns>
    public async Task<IEnumerable<QueryResult>> ExecuteMultipleSqlWithTransactionAsync(
        IDbConnection connection,
        IEnumerable<string> sqlStatements,
        CancellationToken cancellationToken = default)
    {
        var results = new List<QueryResult>();
        var statements = sqlStatements.ToList();
        bool batchTransactionStarted = false;
        
        _logger.LogInformation("正在執行 {Count} 個 SQL 語句（支援交易管理）", statements.Count);

        try
        {
            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            // 使用內建的 TransactionManager

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            // Start a transaction for the batch if not already active
            // Note: Individual statements in ExecuteSqlWithTransactionAsync will handle their own transaction logic
            //if (!transactionManager.HasActiveTransaction)
            //{
            //    await transactionManager.BeginTransactionAsync(connection);
            //    batchTransactionStarted = true;
            //    _logger.LogDebug("為批次執行啟動交易");
            //}

            foreach (var sql in statements)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                // Skip individual transaction management since we're managing it at batch level
                var result = await ExecuteSqlWithTransactionAsync(connection, sql, cancellationToken, skipTransactionManagement: false);
                results.Add(result);

                if (!result.IsSuccess)
                {
                    _logger.LogWarning("SQL 語句執行失敗，停止後續執行: {ErrorMessage}", result.ErrorMessage);

                    // Rollback the batch transaction if we started it
                    if (batchTransactionStarted && _transactionManager.HasActiveTransaction)
                    {
                        try
                        {
                            await _transactionManager.RollbackAsync();
                            _logger.LogInformation("批次執行失敗，已回復交易");
                        }
                        catch (Exception rollbackEx)
                        {
                            _logger.LogError(rollbackEx, "批次執行失敗後回復交易時發生錯誤");
                            // Don't rethrow - we already have the original error in results
                        }
                    }
                    
                    break;
                }
            }

            // Note: Do not auto-commit batch transactions here
            // According to auto-transaction management requirements, transactions should only be committed:
            // 1. Manually by user clicking commit button
            // 2. Automatically for DDL statements (handled in ExecuteSqlWithTransactionAsync)
            // The transaction remains active for user to manually commit or rollback
            if (batchTransactionStarted && results.All(r => r.IsSuccess))
            {
                _logger.LogInformation("批次執行成功，交易保持活躍狀態等待使用者手動提交或回復");
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("批次 SQL 執行已取消");
            
            // Rollback if we started the transaction
            if (batchTransactionStarted && _transactionManager.HasActiveTransaction)
            {
                try
                {
                    await _transactionManager.RollbackAsync();
                    _logger.LogInformation("批次執行取消，已回復交易");
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "回復交易時發生錯誤");
                }
            }
            
            var cancelResult = QueryResult.Failure("批次執行已取消", new OperationCanceledException(), 
                string.Join("; ", statements));
            results.Add(cancelResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "執行多個 SQL 語句時發生錯誤（交易管理）");
            
            // Rollback if we started the transaction
            if (batchTransactionStarted && _transactionManager.HasActiveTransaction)
            {
                try
                {
                    await _transactionManager.RollbackAsync();
                    _logger.LogInformation("批次執行錯誤，已回復交易");
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "回復交易時發生錯誤");
                }
            }
            
            var errorResult = QueryResult.Failure($"批次執行失敗: {ex.Message}", ex, string.Join("; ", statements));
            results.Add(errorResult);
        }

        return results;
    }

    /// <summary>
    /// 執行查詢並確保在正確的交易上下文中執行
    /// </summary>
    private async Task<DataTable> ExecuteQueryWithTransactionAsync(
        IDbConnection connection,
        string sql,
        CancellationToken cancellationToken)
    {
        // 調試資訊：檢查 TransactionManager 狀態
        _logger.LogDebug("ExecuteQueryWithTransactionAsync - TransactionManager HashCode: {HashCode}", _transactionManager.GetHashCode());
        _logger.LogDebug("ExecuteQueryWithTransactionAsync - HasActiveTransaction: {HasActiveTransaction}", _transactionManager.HasActiveTransaction);
        _logger.LogDebug("ExecuteQueryWithTransactionAsync - CurrentState: {CurrentState}", _transactionManager.CurrentState);

        // 使用支援交易的查詢方法
        var transaction = _transactionManager.HasActiveTransaction ? _transactionManager.CurrentTransaction : null;

        _logger.LogDebug("ExecuteQueryWithTransactionAsync - Transaction is null: {IsNull}", transaction == null);

        return await ExecuteQueryWithCancellationAndTransactionAsync(connection, sql, transaction, cancellationToken);
    }

    /// <summary>
    /// 執行非查詢並確保在正確的交易上下文中執行
    /// </summary>
    private async Task<int> ExecuteNonQueryWithTransactionAsync(
        IDbConnection connection,
        string sql,
        CancellationToken cancellationToken)
    {
        // 調試資訊：檢查 TransactionManager 狀態
        _logger.LogDebug("ExecuteNonQueryWithTransactionAsync - TransactionManager HashCode: {HashCode}", _transactionManager.GetHashCode());
        _logger.LogDebug("ExecuteNonQueryWithTransactionAsync - HasActiveTransaction: {HasActiveTransaction}", _transactionManager.HasActiveTransaction);
        _logger.LogDebug("ExecuteNonQueryWithTransactionAsync - CurrentState: {CurrentState}", _transactionManager.CurrentState);

        // 如果有活躍交易，需要確保非查詢在該交易中執行
        if (_transactionManager.HasActiveTransaction && _transactionManager.CurrentTransaction != null)
        {
            _logger.LogDebug("ExecuteNonQueryWithTransactionAsync - Using existing transaction");
            return await ExecuteNonQueryInTransactionAsync(connection, sql, _transactionManager.CurrentTransaction, cancellationToken);
        }
        else
        {
            _logger.LogDebug("ExecuteNonQueryWithTransactionAsync - No active transaction, executing without transaction");
            return await ExecuteNonQueryWithCancellationAsync(connection, sql, cancellationToken);
        }
    }



    /// <summary>
    /// 在指定交易中執行非查詢
    /// </summary>
    private async Task<int> ExecuteNonQueryInTransactionAsync(
        IDbConnection connection,
        string sql,
        IDbTransaction transaction,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("正在執行非查詢語句（交易中），SQL 長度: {SqlLength}", sql?.Length ?? 0);

            using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandTimeout = 300; // 5 分鐘逾時
            command.Transaction = transaction; // 關鍵：指派交易

            var rowsAffected = await Task.Run(() => command.ExecuteNonQuery(), cancellationToken);

            _logger.LogInformation("非查詢語句執行成功（交易中），影響 {RowsAffected} 筆資料", rowsAffected);
            return rowsAffected;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "非查詢語句執行失敗（交易中）");
            throw;
        }
    }

    /// <summary>
    /// 將 Oracle 錯誤轉換為使用者友善的錯誤訊息
    /// </summary>
    /// <param name="ex">Oracle 例外</param>
    /// <returns>使用者友善的錯誤訊息</returns>
    private static string GetUserFriendlyOracleErrorMessage(OracleException ex)
    {
        return ex.Number switch
        {
            900 => "SQL 語法錯誤",
            904 => "無效的識別碼或欄位名稱",
            942 => "資料表或檢視不存在",
            1 => "違反唯一約束條件",
            1400 => "不能將 NULL 值插入必填欄位",
            1722 => "無效的數字格式",
            2291 => "違反參考完整性約束條件",
            12899 => "欄位值過長",
            _ => $"Oracle 錯誤 (ORA-{ex.Number:D5}): {ex.Message}"
        };
    }

    public void Dispose()
    {
        _transactionManager?.Dispose();
    }
}