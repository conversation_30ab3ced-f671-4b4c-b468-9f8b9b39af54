{"Version": 1, "WorkspaceRootPath": "F:\\Projects\\ORMS1\\OracleMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\viewmodels\\connectionmanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\viewmodels\\connectionmanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\views\\connectioneditdialog.xaml.cs||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\views\\connectioneditdialog.xaml.cs||{8B382828-6202-11D1-8870-0000F87579D2}|"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\services\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\services\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\models\\connectioninfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\models\\connectioninfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\services\\connectionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\services\\connectionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\views\\connectioneditdialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\views\\connectioneditdialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\services\\databaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\services\\databaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\79af52176639c5284dddaaa3052cf59d36ff5644fb998b01e73e55f38e873233\\DependencyObject.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\viewmodels\\tableeditorviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\viewmodels\\tableeditorviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|f:\\projects\\orms1\\oraclems\\oraclems\\oraclems.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{C8D65691-60DA-46C3-A294-CC454513DF96}|OracleMS\\OracleMS.csproj|solutionrelative:oraclems\\oraclems.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 33, "Children": [{"$type": "Bookmark", "Name": "ST:132:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:129:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:136:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:137:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:138:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:139:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:140:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:137:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:139:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:140:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:142:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:144:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:145:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:146:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:142:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:143:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:145:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:148:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:149:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:150:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:151:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:139:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:146:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:147:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:128:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ConnectionManagerViewModel.cs", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\ViewModels\\ConnectionManagerViewModel.cs", "RelativeDocumentMoniker": "OracleMS\\ViewModels\\ConnectionManagerViewModel.cs", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\ViewModels\\ConnectionManagerViewModel.cs", "RelativeToolTip": "OracleMS\\ViewModels\\ConnectionManagerViewModel.cs", "ViewState": "AgIAAH8AAAAAAAAAAAASwI0AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T14:52:21.147Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "OracleMS\\MainWindow.xaml.cs", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\MainWindow.xaml.cs", "RelativeToolTip": "OracleMS\\MainWindow.xaml.cs", "ViewState": "AgIAAPsAAAAAAAAAAAAewAMBAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T12:22:26.829Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindow.xaml", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\MainWindow.xaml", "RelativeDocumentMoniker": "OracleMS\\MainWindow.xaml", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\MainWindow.xaml", "RelativeToolTip": "OracleMS\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-22T10:58:32.029Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ConnectionInfo.cs", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Models\\ConnectionInfo.cs", "RelativeDocumentMoniker": "OracleMS\\Models\\ConnectionInfo.cs", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Models\\ConnectionInfo.cs", "RelativeToolTip": "OracleMS\\Models\\ConnectionInfo.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAywCoAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T10:51:38.886Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ConnectionEditDialog.xaml.cs", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Views\\ConnectionEditDialog.xaml.cs", "RelativeDocumentMoniker": "OracleMS\\Views\\ConnectionEditDialog.xaml.cs", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Views\\ConnectionEditDialog.xaml.cs", "RelativeToolTip": "OracleMS\\Views\\ConnectionEditDialog.xaml.cs", "ViewState": "AgIAALEAAAAAAAAAAAAewLsAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T10:41:15.911Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ConnectionEditDialog.xaml", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Views\\ConnectionEditDialog.xaml", "RelativeDocumentMoniker": "OracleMS\\Views\\ConnectionEditDialog.xaml", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Views\\ConnectionEditDialog.xaml", "RelativeToolTip": "OracleMS\\Views\\ConnectionEditDialog.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-22T10:40:58.532Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ConfigurationService.cs", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Services\\ConfigurationService.cs", "RelativeDocumentMoniker": "OracleMS\\Services\\ConfigurationService.cs", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Services\\ConfigurationService.cs", "RelativeToolTip": "OracleMS\\Services\\ConfigurationService.cs", "ViewState": "AgIAABEAAAAAAAAAAAD4vxoAAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T10:38:20.525Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ConnectionService.cs", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Services\\ConnectionService.cs", "RelativeDocumentMoniker": "OracleMS\\Services\\ConnectionService.cs", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Services\\ConnectionService.cs", "RelativeToolTip": "OracleMS\\Services\\ConnectionService.cs", "ViewState": "AgIAAH8AAAAAAAAAAAAywIoAAABfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T10:35:25.357Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "DatabaseService.cs", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Services\\DatabaseService.cs", "RelativeDocumentMoniker": "OracleMS\\Services\\DatabaseService.cs", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\Services\\DatabaseService.cs", "RelativeToolTip": "OracleMS\\Services\\DatabaseService.cs", "ViewState": "AgIAAN0CAAAAAAAAAAAAAOwCAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T19:11:39.807Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "DependencyObject.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\79af52176639c5284dddaaa3052cf59d36ff5644fb998b01e73e55f38e873233\\DependencyObject.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\79af52176639c5284dddaaa3052cf59d36ff5644fb998b01e73e55f38e873233\\DependencyObject.cs", "ViewState": "AgIAAAcCAAAAAAAAAAAewBYCAAAfAAAAAQAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T17:28:34.351Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "TableEditorViewModel.cs", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\ViewModels\\TableEditorViewModel.cs", "RelativeDocumentMoniker": "OracleMS\\ViewModels\\TableEditorViewModel.cs", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\ViewModels\\TableEditorViewModel.cs", "RelativeToolTip": "OracleMS\\ViewModels\\TableEditorViewModel.cs", "ViewState": "AgIAABsBAAAAAAAAAAAAACsBAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T17:23:06.026Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "OracleMS.csproj", "DocumentMoniker": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\OracleMS.csproj", "RelativeDocumentMoniker": "OracleMS\\OracleMS.csproj", "ToolTip": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\OracleMS.csproj", "RelativeToolTip": "OracleMS\\OracleMS.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-21T16:50:51.196Z"}]}]}]}