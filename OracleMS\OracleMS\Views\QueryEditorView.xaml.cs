using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using OracleMS.ViewModels;
using System.IO;
using System.Reflection;
using System.Xml;

namespace OracleMS.Views;

public partial class QueryEditorView : UserControl
{
    private QueryEditorViewModel? ViewModel => DataContext as QueryEditorViewModel;

    public QueryEditorView()
    {
        InitializeComponent();
        InitializeSqlEditor();
        Loaded += OnLoaded;
    }

    private void InitializeSqlEditor()
    {
        // Set up SQL syntax highlighting
        LoadSqlSyntaxHighlighting();
        
        // Set up text binding
        SqlEditor.TextChanged += OnSqlTextChanged;
        
        // Set up cursor position tracking
        SqlEditor.TextArea.Caret.PositionChanged += OnCaretPositionChanged;
        
        // Set up selection tracking
        SqlEditor.TextArea.SelectionChanged += OnSelectionChanged;
        
        // Set up key bindings
        SetupKeyBindings();
        
        // Configure editor options
        SqlEditor.Options.EnableHyperlinks = false;
        SqlEditor.Options.EnableEmailHyperlinks = false;
        SqlEditor.Options.ShowColumnRuler = true;
        SqlEditor.Options.ColumnRulerPosition = 120;
    }

    private void LoadSqlSyntaxHighlighting()
    {
        try
        {
            // 首先嘗試從嵌入式資源載入
            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = "OracleMS.Resources.SQL.xshd";

            using (var stream = assembly.GetManifestResourceStream(resourceName))
            {
                if (stream != null)
                {
                    using (var reader = new XmlTextReader(stream))
                    {
                        var definition = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        SqlEditor.SyntaxHighlighting = definition;
                        System.Diagnostics.Debug.WriteLine("Successfully loaded SQL highlighting from embedded resource");
                        return;
                    }
                }
            }

            // 備用方案：從檔案系統載入
            var xshdPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "SQL.xshd");
            if (File.Exists(xshdPath))
            {
                using (var reader = new XmlTextReader(xshdPath))
                {
                    var definition = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                    SqlEditor.SyntaxHighlighting = definition;
                    System.Diagnostics.Debug.WriteLine($"Successfully loaded SQL highlighting from file: {xshdPath}");
                    return;
                }
            }

            // 第三備用方案：從專案目錄載入（開發時使用）
            var projectPath = Path.Combine(Directory.GetCurrentDirectory(), "Resources", "SQL.xshd");
            if (File.Exists(projectPath))
            {
                using (var reader = new XmlTextReader(projectPath))
                {
                    var definition = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                    SqlEditor.SyntaxHighlighting = definition;
                    System.Diagnostics.Debug.WriteLine($"Successfully loaded SQL highlighting from project: {projectPath}");
                    return;
                }
            }

            // 最終備用方案：使用程式碼中的定義
            System.Diagnostics.Debug.WriteLine("External SQL.xshd not found, using embedded definition");
            CreateEnhancedSqlHighlighting();
        }
        catch (Exception ex)
        {
            // 記錄錯誤並使用基本高亮
            System.Diagnostics.Debug.WriteLine($"Failed to load SQL syntax highlighting: {ex.Message}");
            CreateCustomSqlHighlighting();
        }
    }

    private void CreateEnhancedSqlHighlighting()
    {
        var xshdContent = @"<?xml version=""1.0""?>
<SyntaxDefinition name=""SQL"" extensions="".sql"" xmlns=""http://icsharpcode.net/sharpdevelop/syntaxdefinition/2008"">
  <Color name=""Comment"" foreground=""#008000"" />
  <Color name=""String"" foreground=""#A31515"" />
  <Color name=""Keyword"" foreground=""#0000FF"" fontWeight=""bold"" />
  <Color name=""Function"" foreground=""#FF8C00"" fontWeight=""bold"" />
  <Color name=""DataType"" foreground=""#2B91AF"" fontWeight=""bold"" />
  <Color name=""Operator"" foreground=""#8B008B"" />
  <Color name=""Number"" foreground=""#FF0000"" />

  <RuleSet ignoreCase=""true"">
    <!-- Comments -->
    <Span color=""Comment"" begin=""--"" />
    <Span color=""Comment"" multiline=""true"" begin=""/\*"" end=""\*/"" />

    <!-- Strings -->
    <Span color=""String"">
      <Begin>'</Begin>
      <End>'</End>
      <RuleSet>
        <Span begin=""''"" end="""" />
      </RuleSet>
    </Span>

    <!-- Numbers -->
    <Rule color=""Number"">
      \b0[xX][0-9a-fA-F]+|(\b\d+(\.[0-9]+)?([eE][+-]?[0-9]+)?)\b
    </Rule>

    <!-- SQL Keywords -->
    <Keywords color=""Keyword"">
      <Word>SELECT</Word>
      <Word>FROM</Word>
      <Word>WHERE</Word>
      <Word>INSERT</Word>
      <Word>UPDATE</Word>
      <Word>DELETE</Word>
      <Word>CREATE</Word>
      <Word>DROP</Word>
      <Word>ALTER</Word>
      <Word>TABLE</Word>
      <Word>INDEX</Word>
      <Word>VIEW</Word>
      <Word>PROCEDURE</Word>
      <Word>FUNCTION</Word>
      <Word>TRIGGER</Word>
      <Word>DATABASE</Word>
      <Word>SCHEMA</Word>
      <Word>CONSTRAINT</Word>
      <Word>PRIMARY</Word>
      <Word>FOREIGN</Word>
      <Word>KEY</Word>
      <Word>UNIQUE</Word>
      <Word>NOT</Word>
      <Word>NULL</Word>
      <Word>DEFAULT</Word>
      <Word>CHECK</Word>
      <Word>REFERENCES</Word>
      <Word>ON</Word>
      <Word>CASCADE</Word>
      <Word>RESTRICT</Word>
      <Word>SET</Word>
      <Word>ACTION</Word>
      <Word>INNER</Word>
      <Word>LEFT</Word>
      <Word>RIGHT</Word>
      <Word>FULL</Word>
      <Word>OUTER</Word>
      <Word>JOIN</Word>
      <Word>UNION</Word>
      <Word>ALL</Word>
      <Word>DISTINCT</Word>
      <Word>ORDER</Word>
      <Word>BY</Word>
      <Word>GROUP</Word>
      <Word>HAVING</Word>
      <Word>AS</Word>
      <Word>ASC</Word>
      <Word>DESC</Word>
      <Word>LIMIT</Word>
      <Word>OFFSET</Word>
      <Word>TOP</Word>
      <Word>WITH</Word>
      <Word>CASE</Word>
      <Word>WHEN</Word>
      <Word>THEN</Word>
      <Word>ELSE</Word>
      <Word>END</Word>
      <Word>IF</Word>
      <Word>EXISTS</Word>
      <Word>IN</Word>
      <Word>BETWEEN</Word>
      <Word>LIKE</Word>
      <Word>IS</Word>
      <Word>AND</Word>
      <Word>OR</Word>
      <Word>BEGIN</Word>
      <Word>COMMIT</Word>
      <Word>ROLLBACK</Word>
      <Word>TRANSACTION</Word>
      <Word>GRANT</Word>
      <Word>REVOKE</Word>
      <Word>DECLARE</Word>
      <Word>CURSOR</Word>
      <Word>OPEN</Word>
      <Word>FETCH</Word>
      <Word>CLOSE</Word>
      <Word>DEALLOCATE</Word>
      <Word>EXEC</Word>
      <Word>EXECUTE</Word>
      <Word>RETURN</Word>
      <Word>WHILE</Word>
      <Word>FOR</Word>
      <Word>LOOP</Word>
      <Word>BREAK</Word>
      <Word>CONTINUE</Word>
      <Word>GOTO</Word>
      <Word>LABEL</Word>
      <Word>TRY</Word>
      <Word>CATCH</Word>
      <Word>THROW</Word>
      <Word>RAISERROR</Word>
      <Word>CONNECT</Word>
      <Word>START</Word>
      <Word>PRIOR</Word>
      <Word>LEVEL</Word>
      <Word>ROWNUM</Word>
      <Word>ROWID</Word>
      <Word>DUAL</Word>
      <Word>SYSDATE</Word>
      <Word>SYSTIMESTAMP</Word>
      <Word>USER</Word>
      <Word>UID</Word>
      <Word>USERENV</Word>
      <Word>SEQUENCE</Word>
      <Word>NEXTVAL</Word>
      <Word>CURRVAL</Word>
      <Word>SYNONYM</Word>
      <Word>PACKAGE</Word>
      <Word>BODY</Word>
      <Word>TYPE</Word>
      <Word>OBJECT</Word>
      <Word>VARRAY</Word>
      <Word>NESTED</Word>
      <Word>REF</Word>
      <Word>EXCEPTION</Word>
      <Word>PRAGMA</Word>
      <Word>AUTONOMOUS_TRANSACTION</Word>
      <Word>BULK</Word>
      <Word>COLLECT</Word>
      <Word>FORALL</Word>
      <Word>SAVE</Word>
      <Word>EXCEPTIONS</Word>
      <Word>MERGE</Word>
      <Word>USING</Word>
      <Word>MATCHED</Word>
      <Word>UPSERT</Word>
    </Keywords>

    <!-- Data Types -->
    <Keywords color=""DataType"">
      <Word>VARCHAR</Word>
      <Word>VARCHAR2</Word>
      <Word>CHAR</Word>
      <Word>NCHAR</Word>
      <Word>NVARCHAR2</Word>
      <Word>CLOB</Word>
      <Word>NCLOB</Word>
      <Word>BLOB</Word>
      <Word>BFILE</Word>
      <Word>NUMBER</Word>
      <Word>INTEGER</Word>
      <Word>INT</Word>
      <Word>SMALLINT</Word>
      <Word>DECIMAL</Word>
      <Word>NUMERIC</Word>
      <Word>FLOAT</Word>
      <Word>REAL</Word>
      <Word>DOUBLE</Word>
      <Word>PRECISION</Word>
      <Word>DATE</Word>
      <Word>TIMESTAMP</Word>
      <Word>INTERVAL</Word>
      <Word>YEAR</Word>
      <Word>MONTH</Word>
      <Word>DAY</Word>
      <Word>HOUR</Word>
      <Word>MINUTE</Word>
      <Word>SECOND</Word>
      <Word>TIMEZONE</Word>
      <Word>LOCAL</Word>
      <Word>RAW</Word>
      <Word>LONG</Word>
      <Word>UROWID</Word>
      <Word>XMLTYPE</Word>
      <Word>BOOLEAN</Word>
      <Word>BINARY_INTEGER</Word>
      <Word>PLS_INTEGER</Word>
      <Word>BINARY_FLOAT</Word>
      <Word>BINARY_DOUBLE</Word>
    </Keywords>

    <!-- Built-in Functions -->
    <Keywords color=""Function"">
      <Word>COUNT</Word>
      <Word>SUM</Word>
      <Word>AVG</Word>
      <Word>MIN</Word>
      <Word>MAX</Word>
      <Word>STDDEV</Word>
      <Word>VARIANCE</Word>
      <Word>UPPER</Word>
      <Word>LOWER</Word>
      <Word>INITCAP</Word>
      <Word>LENGTH</Word>
      <Word>SUBSTR</Word>
      <Word>INSTR</Word>
      <Word>REPLACE</Word>
      <Word>TRANSLATE</Word>
      <Word>TRIM</Word>
      <Word>LTRIM</Word>
      <Word>RTRIM</Word>
      <Word>LPAD</Word>
      <Word>RPAD</Word>
      <Word>CONCAT</Word>
      <Word>CHR</Word>
      <Word>ASCII</Word>
      <Word>TO_CHAR</Word>
      <Word>TO_NUMBER</Word>
      <Word>TO_DATE</Word>
      <Word>CAST</Word>
      <Word>CONVERT</Word>
      <Word>DECODE</Word>
      <Word>NVL</Word>
      <Word>NVL2</Word>
      <Word>NULLIF</Word>
      <Word>COALESCE</Word>
      <Word>GREATEST</Word>
      <Word>LEAST</Word>
      <Word>ABS</Word>
      <Word>CEIL</Word>
      <Word>FLOOR</Word>
      <Word>ROUND</Word>
      <Word>TRUNC</Word>
      <Word>MOD</Word>
      <Word>POWER</Word>
      <Word>SQRT</Word>
      <Word>EXP</Word>
      <Word>LN</Word>
      <Word>LOG</Word>
      <Word>SIN</Word>
      <Word>COS</Word>
      <Word>TAN</Word>
      <Word>ASIN</Word>
      <Word>ACOS</Word>
      <Word>ATAN</Word>
      <Word>ATAN2</Word>
      <Word>SIGN</Word>
      <Word>ADD_MONTHS</Word>
      <Word>MONTHS_BETWEEN</Word>
      <Word>NEXT_DAY</Word>
      <Word>LAST_DAY</Word>
      <Word>EXTRACT</Word>
      <Word>CURRENT_DATE</Word>
      <Word>CURRENT_TIMESTAMP</Word>
      <Word>LOCALTIMESTAMP</Word>
      <Word>DBTIMEZONE</Word>
      <Word>SESSIONTIMEZONE</Word>
      <Word>SYS_CONTEXT</Word>
      <Word>ROW_NUMBER</Word>
      <Word>RANK</Word>
      <Word>DENSE_RANK</Word>
      <Word>FIRST_VALUE</Word>
      <Word>LAST_VALUE</Word>
      <Word>LAG</Word>
      <Word>LEAD</Word>
      <Word>LISTAGG</Word>
      <Word>XMLAGG</Word>
      <Word>XMLELEMENT</Word>
      <Word>XMLFOREST</Word>
      <Word>XMLATTRIBUTES</Word>
      <Word>EXTRACTVALUE</Word>
      <Word>XMLQUERY</Word>
      <Word>XMLTABLE</Word>
      <Word>REGEXP_LIKE</Word>
      <Word>REGEXP_REPLACE</Word>
      <Word>REGEXP_SUBSTR</Word>
      <Word>REGEXP_INSTR</Word>
      <Word>REGEXP_COUNT</Word>
    </Keywords>

    <!-- Operators -->
    <Rule color=""Operator"">
      [+\-*/=&lt;&gt;!&amp;|^~%]|(&lt;=)|(&gt;=)|(&lt;&gt;)|(!=)
    </Rule>
  </RuleSet>
</SyntaxDefinition>";

        try
        {
            using var reader = new StringReader(xshdContent);
            using var xmlReader = XmlReader.Create(reader);
            var highlighting = HighlightingLoader.Load(xmlReader, HighlightingManager.Instance);
            SqlEditor.SyntaxHighlighting = highlighting;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to load enhanced SQL highlighting: {ex.Message}");
            // Fallback to basic highlighting
            CreateCustomSqlHighlighting();
        }
    }

    private void CreateCustomSqlHighlighting()
    {
        var xshdContent = @"<?xml version=""1.0""?>
<SyntaxDefinition name=""SQL"" xmlns=""http://icsharpcode.net/sharpdevelop/syntaxdefinition/2008"">
    <Color name=""Comment"" foreground=""Green"" />
    <Color name=""String"" foreground=""Red"" />
    <Color name=""Keyword"" foreground=""Blue"" fontWeight=""bold"" />
    <Color name=""Function"" foreground=""Purple"" />
    <Color name=""Number"" foreground=""DarkBlue"" />
    
    <RuleSet ignoreCase=""true"">
        <Span color=""Comment"" begin=""--"" />
        <Span color=""Comment"" multiline=""true"" begin=""/\*"" end=""\*/"" />
        <Span color=""String"" begin=""'"" end=""'"" />
        <Span color=""String"" begin=""&quot;"" end=""&quot;"" />
        
        <Keywords color=""Keyword"">
            <Word>SELECT</Word>
            <Word>FROM</Word>
            <Word>WHERE</Word>
            <Word>INSERT</Word>
            <Word>UPDATE</Word>
            <Word>DELETE</Word>
            <Word>CREATE</Word>
            <Word>DROP</Word>
            <Word>ALTER</Word>
            <Word>TABLE</Word>
            <Word>INDEX</Word>
            <Word>VIEW</Word>
            <Word>PROCEDURE</Word>
            <Word>FUNCTION</Word>
            <Word>TRIGGER</Word>
            <Word>AND</Word>
            <Word>OR</Word>
            <Word>NOT</Word>
            <Word>NULL</Word>
            <Word>IS</Word>
            <Word>IN</Word>
            <Word>EXISTS</Word>
            <Word>BETWEEN</Word>
            <Word>LIKE</Word>
            <Word>ORDER</Word>
            <Word>BY</Word>
            <Word>GROUP</Word>
            <Word>HAVING</Word>
            <Word>UNION</Word>
            <Word>JOIN</Word>
            <Word>INNER</Word>
            <Word>LEFT</Word>
            <Word>RIGHT</Word>
            <Word>OUTER</Word>
            <Word>ON</Word>
            <Word>AS</Word>
            <Word>DISTINCT</Word>
            <Word>TOP</Word>
            <Word>LIMIT</Word>
            <Word>OFFSET</Word>
            <Word>CASE</Word>
            <Word>WHEN</Word>
            <Word>THEN</Word>
            <Word>ELSE</Word>
            <Word>END</Word>
            <Word>IF</Word>
            <Word>BEGIN</Word>
            <Word>COMMIT</Word>
            <Word>ROLLBACK</Word>
            <Word>TRANSACTION</Word>
        </Keywords>
        
        <Keywords color=""Function"">
            <Word>COUNT</Word>
            <Word>SUM</Word>
            <Word>AVG</Word>
            <Word>MIN</Word>
            <Word>MAX</Word>
            <Word>UPPER</Word>
            <Word>LOWER</Word>
            <Word>SUBSTR</Word>
            <Word>LENGTH</Word>
            <Word>TRIM</Word>
            <Word>LTRIM</Word>
            <Word>RTRIM</Word>
            <Word>REPLACE</Word>
            <Word>CONCAT</Word>
            <Word>TO_CHAR</Word>
            <Word>TO_DATE</Word>
            <Word>TO_NUMBER</Word>
            <Word>SYSDATE</Word>
            <Word>SYSTIMESTAMP</Word>
            <Word>NVL</Word>
            <Word>NVL2</Word>
            <Word>DECODE</Word>
            <Word>COALESCE</Word>
        </Keywords>
        
        <Rule color=""Number"">
            \b0[xX][0-9a-fA-F]+  # hex number
            |    \b
            (    \d+(\.[0-9]+)?   #number with optional floating point
            |    \.[0-9]+         #or just starting with floating point
            )
            ([eE][+-]?[0-9]+)?     # optional exponent
        </Rule>
    </RuleSet>
</SyntaxDefinition>";

        try
        {
            using var reader = new StringReader(xshdContent);
            using var xmlReader = XmlReader.Create(reader);
            var highlighting = HighlightingLoader.Load(xmlReader, HighlightingManager.Instance);
            SqlEditor.SyntaxHighlighting = highlighting;
        }
        catch
        {
            // If custom highlighting fails, continue without highlighting
        }
    }

    private void SetupKeyBindings()
    {
        // F5 - Execute
        InputBindings.Add(new KeyBinding(
            new RelayCommand(() => ViewModel?.ExecuteCommand.Execute(null)),
            Key.F5, ModifierKeys.None));

        // Ctrl+Enter - Execute
        InputBindings.Add(new KeyBinding(
            new RelayCommand(() => ViewModel?.ExecuteCommand.Execute(null)),
            Key.Enter, ModifierKeys.Control));

        // Ctrl+S - Save
        InputBindings.Add(new KeyBinding(
            new RelayCommand(() => ViewModel?.SaveCommand.Execute(null)),
            Key.S, ModifierKeys.Control));

        // Ctrl+N - New
        InputBindings.Add(new KeyBinding(
            new RelayCommand(() => ViewModel?.NewCommand.Execute(null)),
            Key.N, ModifierKeys.Control));

        // Ctrl+O - Open
        InputBindings.Add(new KeyBinding(
            new RelayCommand(() => ViewModel?.OpenCommand.Execute(null)),
            Key.O, ModifierKeys.Control));
    }

    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        // Focus the SQL editor when the view is loaded
        SqlEditor.Focus();
    }

    private void OnSqlTextChanged(object? sender, EventArgs e)
    {
        if (ViewModel != null)
        {
            ViewModel.SqlText = SqlEditor.Text;
        }
    }

    private void OnCaretPositionChanged(object? sender, EventArgs e)
    {
        if (ViewModel != null)
        {
            var location = SqlEditor.TextArea.Caret.Location;
            ViewModel.SetCursorPosition(location.Line, location.Column);
        }
    }

    private void OnSelectionChanged(object? sender, EventArgs e)
    {
        if (ViewModel != null)
        {
            ViewModel.SelectedText = SqlEditor.SelectedText;
        }
    }

    // Event handlers for context menu and toolbar actions
    private void CopyCell_Click(object sender, RoutedEventArgs e)
    {
        if (sender is MenuItem menuItem &&
            menuItem.DataContext is System.Data.DataRowView rowView &&
            ResultsTabControl.SelectedItem != null)
        {
            // Get the selected cell value and copy to clipboard
            var dataGrid = FindVisualChild<DataGrid>(ResultsTabControl);
            if (dataGrid?.SelectedCells.Count > 0)
            {
                var cellValue = dataGrid.SelectedCells[0].Item?.ToString() ?? "";
                Clipboard.SetText(cellValue);
            }
        }
    }

    private void CopyRow_Click(object sender, RoutedEventArgs e)
    {
        var dataGrid = FindVisualChild<DataGrid>(ResultsTabControl);
        if (dataGrid?.SelectedItem is System.Data.DataRowView rowView)
        {
            var values = rowView.Row.ItemArray.Select(item => item?.ToString() ?? "").ToArray();
            var rowText = string.Join("\t", values);
            Clipboard.SetText(rowText);
        }
    }

    private void CopyAll_Click(object sender, RoutedEventArgs e)
    {
        var dataGrid = FindVisualChild<DataGrid>(ResultsTabControl);
        if (dataGrid?.ItemsSource is System.Data.DataView dataView)
        {
            var text = new System.Text.StringBuilder();

            // Add headers
            var headers = dataView.Table.Columns.Cast<System.Data.DataColumn>()
                .Select(col => col.ColumnName).ToArray();
            text.AppendLine(string.Join("\t", headers));

            // Add data rows
            foreach (System.Data.DataRowView rowView in dataView)
            {
                var values = rowView.Row.ItemArray.Select(item => item?.ToString() ?? "").ToArray();
                text.AppendLine(string.Join("\t", values));
            }

            Clipboard.SetText(text.ToString());
        }
    }

    private void ClearMessages_Click(object sender, RoutedEventArgs e)
    {
        if (ViewModel != null)
        {
            ViewModel.Messages = string.Empty;
        }
    }

    private void CopyMessages_Click(object sender, RoutedEventArgs e)
    {
        if (!string.IsNullOrEmpty(ViewModel?.Messages))
        {
            Clipboard.SetText(ViewModel.Messages);
        }
    }

    // Helper method to find visual children
    private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
    {
        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
        {
            var child = VisualTreeHelper.GetChild(parent, i);
            if (child is T result)
                return result;
            
            var childOfChild = FindVisualChild<T>(child);
            if (childOfChild != null)
                return childOfChild;
        }
        return null;
    }

    // Simple relay command for key bindings
    private class RelayCommand : ICommand
    {
        private readonly Action _execute;
        
        public RelayCommand(Action execute)
        {
            _execute = execute;
        }
        
        public bool CanExecute(object? parameter) => true;
        public void Execute(object? parameter) => _execute();
        public event EventHandler? CanExecuteChanged;
    }


}