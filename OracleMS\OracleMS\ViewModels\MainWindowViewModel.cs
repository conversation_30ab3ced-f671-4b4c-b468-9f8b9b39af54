using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.Services;

namespace OracleMS.ViewModels;

public class MainWindowViewModel : ViewModelBase
{
    private readonly IConnectionService _connectionService;
    private readonly IDatabaseService _databaseService;
    private readonly IScriptGeneratorService _scriptGeneratorService;

    // 資料庫連線建立事件
    public event EventHandler<DatabaseConnectionEventArgs>? DatabaseConnectionEstablished;

    // File Menu Commands
    public ICommand NewConnectionCommand { get; private set; }
    public ICommand NewQueryCommand { get; private set; }
    public ICommand SaveCommand { get; private set; }
    public ICommand SaveAsCommand { get; private set; }
    public ICommand ExitCommand { get; private set; }
    
    // Connection Management Commands
    public ICommand DeleteConnectionCommand { get; private set; }
    
    // Edit Menu Commands
    public ICommand UndoCommand { get; private set; }
    public ICommand RedoCommand { get; private set; }
    public ICommand CutCommand { get; private set; }
    public ICommand CopyCommand { get; private set; }
    public ICommand PasteCommand { get; private set; }
    public ICommand FindCommand { get; private set; }
    public ICommand ReplaceCommand { get; private set; }
    
    // Query Menu Commands
    public ICommand ExecuteQueryCommand { get; private set; }
    public ICommand ExecuteSelectedQueryCommand { get; private set; }
    public ICommand StopExecutionCommand { get; private set; }
    public ICommand ShowExecutionPlanCommand { get; private set; }
    
    // Tools Menu Commands
    public ICommand ShowConnectionManagerCommand { get; private set; }
    public ICommand ShowObjectExplorerCommand { get; private set; }
    public ICommand ShowScriptGeneratorCommand { get; private set; }
    public ICommand ShowOptionsCommand { get; private set; }
    
    // Window Menu Commands
    public ICommand ResetLayoutCommand { get; private set; }
    public ICommand CloseAllTabsCommand { get; private set; }
    public ICommand CloseOtherTabsCommand { get; private set; }
    
    // Help Menu Commands
    public ICommand ShowAboutCommand { get; private set; }
    public ICommand ShowHelpCommand { get; private set; }
    
    // Tab Management
    public ICommand CloseTabCommand { get; private set; }
    
    public ObservableCollection<TabItemViewModel> OpenTabs { get; } = new();
    public ObservableCollection<object> Documents { get; } = new();
    public ObservableCollection<object> Anchorables { get; } = new();
    
    // Connection List
    private ObservableCollection<ConnectionInfo> _savedConnections = new();
    public ObservableCollection<ConnectionInfo> SavedConnections
    {
        get => _savedConnections;
        set => SetProperty(ref _savedConnections, value);
    }
    
    private bool _isLoadingConnections;
    public bool IsLoadingConnections
    {
        get => _isLoadingConnections;
        set => SetProperty(ref _isLoadingConnections, value);
    }
    
    private TabItemViewModel? _selectedTab;
    public TabItemViewModel? SelectedTab
    {
        get => _selectedTab;
        set => SetProperty(ref _selectedTab, value);
    }

    private string _statusMessage = "就緒";
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    private string _connectionStatus = "未連線";
    public string ConnectionStatus
    {
        get => _connectionStatus;
        set => SetProperty(ref _connectionStatus, value);
    }

    private DateTime _currentTime = DateTime.Now;
    public DateTime CurrentTime
    {
        get => _currentTime;
        set => SetProperty(ref _currentTime, value);
    }

    private bool _isConnected;
    public bool IsConnected
    {
        get => _isConnected;
        set => SetProperty(ref _isConnected, value);
    }

    public MainWindowViewModel(
        IConnectionService connectionService,
        IDatabaseService databaseService,
        IScriptGeneratorService scriptGeneratorService)
    {
        _connectionService = connectionService;
        _databaseService = databaseService;
        _scriptGeneratorService = scriptGeneratorService;

        // Initialize all commands
        InitializeCommands();
        
        // Subscribe to connection state changes
        _connectionService.ConnectionStateChanged += OnConnectionStateChanged;
        
        // Subscribe to connection service events to update the list when connections change
        _connectionService.ConnectionStateChanged += (sender, args) => 
        {
            // Refresh the connection list when connections are added or modified
            if (args.State == Services.ConnectionStatus.Connected || args.State == Services.ConnectionStatus.Disconnected)
            {
                RefreshConnectionListAsync();
            }
        };
        
        // Start timer for current time updates
        var timer = new System.Windows.Threading.DispatcherTimer();
        timer.Interval = TimeSpan.FromSeconds(1);
        timer.Tick += (s, e) => CurrentTime = DateTime.Now;
        timer.Start();
        
        // Load saved connections
        LoadSavedConnectionsAsync();
    }

    private void InitializeCommands()
    {
        // File Menu Commands
        NewConnectionCommand = new RelayCommand(OnNewConnection);
        NewQueryCommand = new RelayCommand(OnNewQuery);
        SaveCommand = new RelayCommand(OnSave, () => SelectedTab != null);
        SaveAsCommand = new RelayCommand(OnSaveAs, () => SelectedTab != null);
        ExitCommand = new RelayCommand(OnExit);
        
        // Connection Management Commands
        DeleteConnectionCommand = new RelayCommand<string>(OnDeleteConnection);
        
        // Edit Menu Commands
        UndoCommand = new RelayCommand(OnUndo, CanUndo);
        RedoCommand = new RelayCommand(OnRedo, CanRedo);
        CutCommand = new RelayCommand(OnCut, CanCut);
        CopyCommand = new RelayCommand(OnCopy, CanCopy);
        PasteCommand = new RelayCommand(OnPaste, CanPaste);
        FindCommand = new RelayCommand(OnFind);
        ReplaceCommand = new RelayCommand(OnReplace);
        
        // Query Menu Commands
        ExecuteQueryCommand = new RelayCommand(OnExecuteQuery, () => IsConnected);
        ExecuteSelectedQueryCommand = new RelayCommand(OnExecuteSelectedQuery, () => IsConnected);
        StopExecutionCommand = new RelayCommand(OnStopExecution);
        ShowExecutionPlanCommand = new RelayCommand(OnShowExecutionPlan, () => IsConnected);
        
        // Tools Menu Commands
        ShowConnectionManagerCommand = new RelayCommand(OnShowConnectionManager);
        ShowObjectExplorerCommand = new RelayCommand(OnShowObjectExplorer);
        ShowScriptGeneratorCommand = new RelayCommand(OnShowScriptGenerator);
        ShowOptionsCommand = new RelayCommand(OnShowOptions);
        
        // Window Menu Commands
        ResetLayoutCommand = new RelayCommand(OnResetLayout);
        CloseAllTabsCommand = new RelayCommand(OnCloseAllTabs, () => OpenTabs.Any());
        CloseOtherTabsCommand = new RelayCommand(OnCloseOtherTabs, () => OpenTabs.Count > 1);
        
        // Help Menu Commands
        ShowAboutCommand = new RelayCommand(OnShowAbout);
        ShowHelpCommand = new RelayCommand(OnShowHelp);
        
        // Tab Management
        CloseTabCommand = new RelayCommand<TabItemViewModel>(OnCloseTab);
    }

    // File Menu Command Implementations
    private void OnNewConnection()
    {
        try
        {
            // Create a new connection info
            var newConnection = new Models.ConnectionInfo
            {
                Id = Guid.NewGuid().ToString(),
                Name = "新連線",
                Server = "*************",
                Port = 1521,
                ServiceName = "AFLC",
                Username = "AFDP45",
                Password = "AFDP45",
                ConnectionTimeout = 30,
                SavePassword = false,
                AutoConnect = false
            };

            // Create connection manager view model
            var connectionManagerViewModel = new ConnectionManagerViewModel(_connectionService);
            
            // Show connection edit dialog
            var dialog = new Views.ConnectionEditDialog(newConnection, connectionManagerViewModel);
            dialog.Owner = System.Windows.Application.Current.MainWindow;
            
            var dialogResult = dialog.ShowDialog();
            System.Diagnostics.Debug.WriteLine($"Dialog result: {dialogResult}");
            System.Diagnostics.Debug.WriteLine($"DatabaseConnection is null: {dialog.DatabaseConnection == null}");

            if (dialogResult == true && dialog.DatabaseConnection != null)
            {
                // Add the new connection
                connectionManagerViewModel.EditingConnection = newConnection;
                connectionManagerViewModel.SaveConnectionCommand.Execute(null); // ConnectionService.SaveConnectionAsync 已經有存檔不需要重複
                StatusMessage = $"已新增連線: {newConnection.Name}";

                // 通知 MainWindow 有新的資料庫連線可用
                System.Diagnostics.Debug.WriteLine("Triggering DatabaseConnectionEstablished event");
                DatabaseConnectionEstablished?.Invoke(this, new DatabaseConnectionEventArgs(dialog.DatabaseConnection, newConnection));
                //if (connectionManagerViewModel.SaveConnectionCommand.CanExecute(null))
                //{
                //    connectionManagerViewModel.SaveConnectionCommand.Execute(null);
                //    StatusMessage = $"已新增連線: {newConnection.Name}";

                //    // 通知 MainWindow 有新的資料庫連線可用
                //    System.Diagnostics.Debug.WriteLine("Triggering DatabaseConnectionEstablished event");
                //    DatabaseConnectionEstablished?.Invoke(this, new DatabaseConnectionEventArgs(dialog.DatabaseConnection, newConnection));
                //}
                //else
                //{
                //    System.Diagnostics.Debug.WriteLine("SaveConnectionCommand cannot execute");
                //}
            }
            else
            {
                if (dialogResult == true)
                {
                    StatusMessage = "連線建立失敗";
                }
                else
                {
                    StatusMessage = "取消新增連線";
                }
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"新增連線失敗: {ex.Message}";
            System.Windows.MessageBox.Show($"新增連線時發生錯誤: {ex.Message}", "錯誤", 
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    private void OnNewQuery()
    {
        var transactionManager = App.ServiceProvider.GetService<ITransactionManager>();
        if (transactionManager == null)
            throw new InvalidOperationException("ITransactionManager service not registered");
            
        var queryTab = new TabItemViewModel
        {
            Header = $"查詢 {OpenTabs.Count + 1}",
            Content = new QueryEditorViewModel(_databaseService, () => null), // TODO: 需要提供正確的連線獲取函數
            CanClose = true
        };
        
        OpenTabs.Add(queryTab);
        SelectedTab = queryTab;
        StatusMessage = "開啟新查詢";
    }

    private void OnSaveAs()
    {
        if (SelectedTab?.Content is ISaveable saveable)
        {
            // In a real implementation, show save file dialog
            saveable.Save();
            SelectedTab.HasUnsavedChanges = false;
            StatusMessage = "已另存新檔";
        }
    }

    // Edit Menu Command Implementations
    private void OnUndo()
    {
        StatusMessage = "復原";
    }

    private void OnRedo()
    {
        StatusMessage = "重做";
    }

    private void OnCut()
    {
        StatusMessage = "剪下";
    }

    private void OnCopy()
    {
        StatusMessage = "複製";
    }

    private void OnPaste()
    {
        StatusMessage = "貼上";
    }

    private void OnFind()
    {
        StatusMessage = "尋找";
    }

    private void OnReplace()
    {
        StatusMessage = "取代";
    }

    // Query Menu Command Implementations
    private void OnExecuteQuery()
    {
        if (SelectedTab?.Content is QueryEditorViewModel queryEditor)
        {
            // Execute the query
            StatusMessage = "執行查詢中...";
        }
    }

    private void OnExecuteSelectedQuery()
    {
        if (SelectedTab?.Content is QueryEditorViewModel queryEditor)
        {
            // Execute selected query
            StatusMessage = "執行選取的查詢中...";
        }
    }

    private void OnStopExecution()
    {
        StatusMessage = "停止執行";
    }

    private void OnShowExecutionPlan()
    {
        StatusMessage = "顯示執行計畫";
    }

    // Tools Menu Command Implementations
    private void OnShowConnectionManager()
    {
        // Check if connection manager is already open
        var existingTab = OpenTabs.FirstOrDefault(t => t.Content is ConnectionManagerViewModel);
        if (existingTab != null)
        {
            SelectedTab = existingTab;
        }
        else
        {
            OnNewConnection();
        }
    }

    private void OnShowObjectExplorer()
    {
        // Object Explorer is already visible in the main window layout
        // This command could be used to toggle visibility or focus the object explorer
        StatusMessage = "物件瀏覽器已顯示";
        
        // If there are active connections, ensure ObjectExplorer is updated
        var activeConnections = GetActiveConnections().ToList();
        if (activeConnections.Any())
        {
            StatusMessage = $"物件瀏覽器已顯示 - 已連線到 {activeConnections.Count} 個資料庫";
        }
    }

    private void OnShowScriptGenerator()
    {
        StatusMessage = "顯示腳本產生器";
    }

    private void OnShowOptions()
    {
        StatusMessage = "顯示選項";
    }

    // Window Menu Command Implementations
    private void OnResetLayout()
    {
        StatusMessage = "重設視窗配置";
    }

    private void OnCloseAllTabs()
    {
        var tabsToClose = OpenTabs.Where(t => t.CanClose).ToList();
        foreach (var tab in tabsToClose)
        {
            OpenTabs.Remove(tab);
        }
        SelectedTab = OpenTabs.FirstOrDefault();
        StatusMessage = "已關閉所有標籤";
    }

    private void OnCloseOtherTabs()
    {
        if (SelectedTab == null) return;
        
        var tabsToClose = OpenTabs.Where(t => t != SelectedTab && t.CanClose).ToList();
        foreach (var tab in tabsToClose)
        {
            OpenTabs.Remove(tab);
        }
        StatusMessage = "已關閉其他標籤";
    }

    // Help Menu Command Implementations
    private void OnShowAbout()
    {
        StatusMessage = "Oracle Management Studio v1.0";
    }

    private void OnShowHelp()
    {
        StatusMessage = "顯示說明";
    }

    // Command Can Execute Methods
    private bool CanUndo() => false; // Implement based on current editor state
    private bool CanRedo() => false; // Implement based on current editor state
    private bool CanCut() => false; // Implement based on current selection
    private bool CanCopy() => false; // Implement based on current selection
    private bool CanPaste() => true; // Implement based on clipboard content

    private void OnCloseTab(TabItemViewModel? tab)
    {
        if (tab == null) return;

        // Check if tab has unsaved changes
        if (tab.HasUnsavedChanges)
        {
            // In a real implementation, show a dialog asking to save
            // For now, just close the tab
        }

        OpenTabs.Remove(tab);
        
        if (SelectedTab == tab)
        {
            SelectedTab = OpenTabs.LastOrDefault();
        }

        StatusMessage = $"已關閉標籤頁: {tab.Header}";
    }

    private void OnSave()
    {
        if (SelectedTab?.Content is ISaveable saveable)
        {
            saveable.Save();
            SelectedTab.HasUnsavedChanges = false;
            StatusMessage = "已儲存";
        }
    }

    private void OnExit()
    {
        // Check for unsaved changes before exiting
        var unsavedTabs = OpenTabs.Where(t => t.HasUnsavedChanges).ToList();
        if (unsavedTabs.Any())
        {
            // In a real implementation, show a dialog asking to save
        }
        
        System.Windows.Application.Current.Shutdown();
    }

    public void AddTab(TabItemViewModel tab)
    {
        OpenTabs.Add(tab);
        SelectedTab = tab;
    }

    public void SetConnectionStatus(bool isConnected, string? connectionName = null)
    {
        IsConnected = isConnected;
        ConnectionStatus = isConnected 
            ? $"已連線到: {connectionName ?? "資料庫"}" 
            : "未連線";
    }

    public void OnWindowClosing(CancelEventArgs e)
    {
        // Handle window closing logic
        var unsavedTabs = OpenTabs.Where(t => t.HasUnsavedChanges).ToList();
        if (unsavedTabs.Any())
        {
            // In a real implementation, show a dialog asking to save
            // For now, allow closing
        }
        
        // Close all active connections
        _connectionService.CloseAllConnections();
    }

    /// <summary>
    /// 處理連線狀態變更事件
    /// </summary>
    private void OnConnectionStateChanged(object? sender, Services.ConnectionStateChangedEventArgs e)
    {
        // Update connection status based on state
        switch (e.State)
        {
            case Services.ConnectionStatus.Connected:
                SetConnectionStatus(true, e.ConnectionInfo.Name);
                StatusMessage = $"已連線到: {e.ConnectionInfo.Name}";
                break;
            case Services.ConnectionStatus.Disconnected:
                // Check if there are any other active connections
                var activeConnections = _connectionService.GetActiveConnections().ToList();
                if (activeConnections.Any())
                {
                    var firstActive = activeConnections.First();
                    SetConnectionStatus(true, firstActive.Name);
                }
                else
                {
                    SetConnectionStatus(false);
                }
                StatusMessage = string.IsNullOrEmpty(e.ErrorMessage) 
                    ? $"已中斷連線: {e.ConnectionInfo.Name}"
                    : $"連線中斷: {e.ConnectionInfo.Name} - {e.ErrorMessage}";
                break;
            case Services.ConnectionStatus.Reconnected:
                SetConnectionStatus(true, e.ConnectionInfo.Name);
                StatusMessage = $"重新連線成功: {e.ConnectionInfo.Name}";
                break;
            case Services.ConnectionStatus.Failed:
                StatusMessage = $"連線失敗: {e.ConnectionInfo.Name} - {e.ErrorMessage}";
                break;
        }

        // Update command states
        ((RelayCommand)ExecuteQueryCommand).NotifyCanExecuteChanged();
        ((RelayCommand)ExecuteSelectedQueryCommand).NotifyCanExecuteChanged();
        ((RelayCommand)ShowExecutionPlanCommand).NotifyCanExecuteChanged();
    }

    /// <summary>
    /// 取得目前活動的連線清單
    /// </summary>
    public IEnumerable<Models.ConnectionInfo> GetActiveConnections()
    {
        return _connectionService.GetActiveConnections();
    }

    /// <summary>
    /// 中斷指定連線
    /// </summary>
    public void DisconnectConnection(string connectionId)
    {
        _connectionService.CloseConnection(connectionId);
    }
    
    // Property to track connection in progress
    private bool _isConnecting;
    public bool IsConnecting
    {
        get => _isConnecting;
        set => SetProperty(ref _isConnecting, value);
    }
    
    // Property to track which connection is being established
    private string _connectingConnectionId;
    public string ConnectingConnectionId
    {
        get => _connectingConnectionId;
        set => SetProperty(ref _connectingConnectionId, value);
    }
    
    /// <summary>
    /// 建立指定ID的連線
    /// </summary>
    /// <param name="connectionId">連線ID</param>
    public async Task<IDbConnection> CreateManagedConnectionAsync(string connectionId)
    {
        try
        {
            // Set connecting state
            IsConnecting = true;
            ConnectingConnectionId = connectionId;
            StatusMessage = $"正在連線到 {connectionId}...";
            
            // Find the connection info by ID
            var connectionInfo = SavedConnections.FirstOrDefault(c => c.Id == connectionId);
            if (connectionInfo == null)
            {
                throw new InvalidOperationException($"找不到連線: {connectionId}");
            }
            
            // Create the connection
            var connection = await _connectionService.CreateManagedConnectionAsync(connectionInfo);
            StatusMessage = $"已連線到 {connectionId}";
            
            // Raise the DatabaseConnectionEstablished event to create a new session tab
            DatabaseConnectionEstablished?.Invoke(this, new DatabaseConnectionEventArgs(connection, connectionInfo));
            
            return connection;
        }
        catch (InvalidOperationException ex)
        {
            // Handle specific case where connection info is not found
            StatusMessage = $"連線失敗: {ex.Message}";
            throw;
        }
        catch (System.Data.Common.DbException ex)
        {
            // Handle database-specific exceptions
            string errorDetails = ex.Message;
            
            // Log the error for debugging purposes
            System.Diagnostics.Debug.WriteLine($"Database connection error: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"Error source: {ex.Source}");
            System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            
            StatusMessage = $"連線失敗: {connectionId}";
            throw;
        }
        catch (TimeoutException ex)
        {
            // Handle timeout exceptions specifically
            StatusMessage = $"連線逾時: {connectionId}";
            throw;
        }
        catch (Exception ex)
        {
            // Handle all other exceptions
            StatusMessage = $"連線失敗: {ex.Message}";
            
            // Log the error for debugging purposes
            System.Diagnostics.Debug.WriteLine($"Unexpected connection error: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"Error type: {ex.GetType().Name}");
            System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            
            throw;
        }
        finally
        {
            // Clear connecting state
            IsConnecting = false;
            ConnectingConnectionId = null;
        }
    }
    
    /// <summary>
    /// 載入已儲存的連線清單
    /// </summary>
    private async void LoadSavedConnectionsAsync()
    {
        try
        {
            // Set loading state
            IsLoadingConnections = true;
            StatusMessage = "正在載入連線清單...";
            
            // Load connections asynchronously
            var connections = await _connectionService.GetSavedConnectionsAsync();
            
            // Update the ObservableCollection on the UI thread
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                SavedConnections.Clear();
                foreach (var connection in connections)
                {
                    SavedConnections.Add(connection);
                }
                
                StatusMessage = SavedConnections.Count > 0 
                    ? $"已載入 {SavedConnections.Count} 個連線" 
                    : "沒有可用的連線";
                
                // Clear loading state
                IsLoadingConnections = false;
            });
        }
        catch (Exception ex)
        {
            // Update UI on error
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                StatusMessage = "載入連線清單失敗";
                IsLoadingConnections = false;
            });
            
            System.Diagnostics.Debug.WriteLine($"Error loading connections: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 重新整理連線清單
    /// </summary>
    public async void RefreshConnectionListAsync()
    {
        await Task.Run(() => LoadSavedConnectionsAsync());
    }
    
    /// <summary>
    /// 公共方法用於測試刪除連線功能
    /// </summary>
    /// <param name="connectionId">連線ID</param>
    public void DeleteConnection(string connectionId)
    {
        OnDeleteConnection(connectionId);
    }
    
    /// <summary>
    /// 刪除指定的連線
    /// </summary>
    /// <param name="connectionId">要刪除的連線ID</param>
    private async void OnDeleteConnection(string connectionId)
    {
        try
        {
            if (string.IsNullOrEmpty(connectionId))
            {
                return;
            }
                
            // 找到要刪除的連線
            var connectionToDelete = SavedConnections.FirstOrDefault(c => c.Id == connectionId);
            if (connectionToDelete == null)
            {
                StatusMessage = "找不到指定的連線";
                return;
            }
            
            // 確認是否要刪除
            var result = System.Windows.MessageBox.Show(
                $"確定要刪除連線 '{connectionToDelete.Id}' 嗎？此操作無法復原。",
                "刪除連線",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Question);
                
            if (result != System.Windows.MessageBoxResult.Yes)
            {
                StatusMessage = "取消刪除連線";
                return;
            }

            StatusMessage = "正在刪除連線...";

            // 如果連線是活動的，先中斷連線
            var activeConnections = _connectionService.GetActiveConnections().ToList();
            if (activeConnections.Any(c => c.Id == connectionId))
            {
                _connectionService.CloseConnection(connectionId);
                StatusMessage = "已中斷活動連線，正在刪除...";
            }
            
            // 刪除連線
            await _connectionService.DeleteConnectionAsync(connectionId);

            // 從列表中移除
            var connectionInList = SavedConnections.FirstOrDefault(c => c.Id == connectionId);
            if (connectionInList != null)
            {
                SavedConnections.Remove(connectionInList);
                StatusMessage = $"已刪除連線: {connectionToDelete.Id}";
            }
            else
            {
                StatusMessage = $"警告：連線已從儲存中刪除，但未在 UI 列表中找到: {connectionId}";
            }
            
            // 檢查是否需要更新連線狀態
            if (activeConnections.Any(c => c.Id == connectionId))
            {
                // 如果刪除的是當前活動連線，更新連線狀態
                var remainingConnections = _connectionService.GetActiveConnections().ToList();
                if (remainingConnections.Any())
                {
                    var firstActive = remainingConnections.First();
                    SetConnectionStatus(true, firstActive.Name);
                }
                else
                {
                    SetConnectionStatus(false);
                }
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"刪除連線失敗: {ex.Message}";
            System.Windows.MessageBox.Show($"刪除連線時發生錯誤: {ex.Message}", "錯誤", 
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }
}

public class TabItemViewModel : ViewModelBase
{
    private string _header = string.Empty;
    public string Header
    {
        get => _header;
        set => SetProperty(ref _header, value);
    }

    private object? _content;
    public object? Content
    {
        get => _content;
        set => SetProperty(ref _content, value);
    }

    private bool _canClose = true;
    public bool CanClose
    {
        get => _canClose;
        set => SetProperty(ref _canClose, value);
    }

    private bool _hasUnsavedChanges;
    public bool HasUnsavedChanges
    {
        get => _hasUnsavedChanges;
        set => SetProperty(ref _hasUnsavedChanges, value);
    }
}

// 資料庫連線事件參數
public class DatabaseConnectionEventArgs : EventArgs
{
    public IDbConnection Connection { get; }
    public ConnectionInfo ConnectionInfo { get; }

    public DatabaseConnectionEventArgs(IDbConnection connection, ConnectionInfo connectionInfo)
    {
        Connection = connection;
        ConnectionInfo = connectionInfo;
    }
}

