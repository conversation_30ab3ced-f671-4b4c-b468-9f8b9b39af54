{"format": 1, "restore": {"F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\OracleMS.csproj": {}}, "projects": {"F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\OracleMS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\OracleMS.csproj", "projectName": "OracleMS", "projectPath": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\OracleMS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Projects\\ORMS1\\OracleMS\\OracleMS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://www.nuget.org/api/v2": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"AvalonEdit": {"target": "Package", "version": "[6.3.0.90, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "DataGridExtensions": {"target": "Package", "version": "[2.6.0, )"}, "Dirkster.AvalonDock": {"target": "Package", "version": "[4.72.1, )"}, "Extended.Wpf.Toolkit": {"target": "Package", "version": "[4.6.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[23.9.1, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}