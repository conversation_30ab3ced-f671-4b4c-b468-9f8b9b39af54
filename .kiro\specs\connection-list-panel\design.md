# Design Document: Connection List Panel

## Overview

The Connection List Panel feature will replace the current placeholder text in the property panel with a functional ListView component that displays connection data from settings.json. Users will be able to view available connection IDs and open new database sessions by double-clicking on a connection ID. This feature enhances the user experience by providing quick access to saved database connections directly from the main interface.

## Architecture

The Connection List Panel will be integrated into the existing MainWindow.xaml UI, specifically in the right panel area where the placeholder text "屬性面板將在此顯示" currently exists. The feature will leverage the existing connection management infrastructure, including:

1. **IConnectionService** - For retrieving saved connections and creating new connections
2. **IConfigurationService** - For accessing settings.json data
3. **MainWindowViewModel** - For handling UI events and commands

The feature will follow the MVVM (Model-View-ViewModel) pattern consistent with the rest of the application:
- **Model**: ConnectionInfo objects from settings.json
- **View**: ListView control in MainWindow.xaml
- **ViewModel**: Extension to MainWindowViewModel to handle connection list operations

## Components and Interfaces

### View Components

1. **ConnectionListView**
   - A ListView control that displays connection IDs
   - Styling consistent with the application's design
   - Double-click event handler for opening database sessions
   - Visual feedback on hover (to indicate interactivity)
   - Scrolling capability for handling multiple connections

2. **UI Integration**
   - Replace the placeholder TextBlock in MainWindow.xaml
   - Maintain the existing grid structure and border styling

### ViewModel Extensions

1. **MainWindowViewModel Additions**
   - `ObservableCollection<ConnectionInfo> SavedConnections` property to bind to the ListView
   - Method to load saved connections from the ConnectionService
   - Method to refresh the connection list when connections are added/modified
   - Command to handle double-click events on connection items

2. **Event Handling**
   - Double-click event on connection items to open a new database session
   - Connection list refresh when connections are added or modified

### Service Integration

1. **ConnectionService Integration**
   - Use existing `GetSavedConnectionsAsync()` method to retrieve connections
   - Use existing `CreateManagedConnectionAsync()` method to create new connections
   - Subscribe to ConnectionService events to update the list when connections change

2. **Database Session Creation**
   - Leverage existing `OnDatabaseConnectionEstablished` event and `CreateNewDbSessionTab` method
   - Ensure proper error handling for connection failures

## Data Models

The feature will use the existing `ConnectionInfo` class, which already contains all necessary information:

```csharp
public class ConnectionInfo
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Server { get; set; }
    public int Port { get; set; }
    public string ServiceName { get; set; }
    public string Username { get; set; }
    public string Password { get; set; }
    public ConnectionType Type { get; set; }
    public bool SavePassword { get; set; }
    public DateTime LastConnected { get; set; }
    public string Description { get; set; }
    public int ConnectionTimeout { get; set; }
    public bool AutoConnect { get; set; }
    
    // Methods
    public (bool IsValid, string ErrorMessage) Validate()
    public string BuildConnectionString()
    public ConnectionInfo CloneWithoutPassword()
}
```

For the ListView, we'll primarily display the `Id` property, which contains the connection identifier.

## Error Handling

1. **Connection Loading Errors**
   - If loading connections from settings.json fails, display an empty list with an appropriate message
   - Log errors using the application's logging system
   - Provide visual feedback to indicate loading failure

2. **Connection Establishment Errors**
   - If opening a connection fails, display an error message to the user
   - Prevent UI freezing during connection attempts by using async operations
   - Provide detailed error information to help users troubleshoot connection issues

3. **Empty Connection List**
   - Display an appropriate message when no connections are available
   - Provide guidance on how to create new connections

## Testing Strategy

1. **Unit Tests**
   - Test ViewModel methods for loading and refreshing connections
   - Test connection list filtering and sorting (if implemented)
   - Test error handling scenarios

2. **Integration Tests**
   - Test interaction between ConnectionListView and ConnectionService
   - Test connection establishment flow from double-click to database session creation
   - Test UI updates when connections are added or modified

3. **UI Tests**
   - Test ListView appearance and behavior with various numbers of connections
   - Test scrolling behavior with many connections
   - Test visual feedback on hover and selection

## Implementation Considerations

1. **Performance**
   - Load connections asynchronously to prevent UI freezing
   - Implement virtualization for the ListView if many connections are expected
   - Consider caching connection data to reduce service calls

2. **User Experience**
   - Provide visual feedback during connection attempts
   - Ensure consistent styling with the rest of the application
   - Consider adding tooltips with additional connection information on hover

3. **Extensibility**
   - Design the feature to accommodate future enhancements, such as:
     - Connection filtering or searching
     - Connection sorting options
     - Right-click context menu with additional actions
     - Connection status indicators

## UI Mockup

```
+----------------------------------+
| Connection Records               |
+----------------------------------+
| user1@service1/server1           |
| user2@service2/server2           |
| user3@service3/server3           |
| ...                              |
+----------------------------------+
```

The ListView will be styled consistently with the application's design, with appropriate padding, font styles, and hover effects to indicate interactivity.