using System.Data;
using OracleMS.Interfaces;

namespace OracleMS.Models;

public class QueryResult
{
    public DataTable? Data { get; set; }
    public int RowsAffected { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public bool IsSuccess { get; set; }
    public QueryType QueryType { get; set; }
    public string SqlStatement { get; set; } = string.Empty;
    public DateTime ExecutedAt { get; set; } = DateTime.Now;
    public Exception? Exception { get; set; }

    // Transaction-related properties
    public bool TransactionStarted { get; set; }
    public bool TransactionCommitted { get; set; }
    public bool TransactionRolledBack { get; set; }
    public TransactionState TransactionState { get; set; } = TransactionState.None;

    // Pagination-related properties
    public string SelectStatement { get; set; } = string.Empty;
    public Dictionary<string, object>? Parameters { get; set; }
    public int PageSize { get; set; } = 1000;
    public int CurrentPage { get; set; } = 1;
    public long TotalRows { get; set; } = 0;
    public bool HasMoreData { get; set; } = false;
    public bool IsPagedResult { get; set; } = false;

    /// <summary>
    /// 建立成功的查詢結果
    /// </summary>
    /// <param name="data">查詢資料</param>
    /// <param name="executionTime">執行時間</param>
    /// <param name="sqlStatement">SQL 語句</param>
    /// <returns>成功的查詢結果</returns>
    public static QueryResult Success(DataTable? data, TimeSpan executionTime, string sqlStatement)
    {
        return new QueryResult
        {
            Data = data,
            RowsAffected = data?.Rows.Count ?? 0,
            ExecutionTime = executionTime,
            IsSuccess = true,
            QueryType = DetermineQueryType(sqlStatement),
            SqlStatement = sqlStatement,
            ExecutedAt = DateTime.Now
        };
    }

    /// <summary>
    /// 建立成功的非查詢結果（INSERT, UPDATE, DELETE）
    /// </summary>
    /// <param name="rowsAffected">受影響的資料列數</param>
    /// <param name="executionTime">執行時間</param>
    /// <param name="sqlStatement">SQL 語句</param>
    /// <returns>成功的非查詢結果</returns>
    public static QueryResult Success(int rowsAffected, TimeSpan executionTime, string sqlStatement)
    {
        return new QueryResult
        {
            Data = null,
            RowsAffected = rowsAffected,
            ExecutionTime = executionTime,
            IsSuccess = true,
            QueryType = DetermineQueryType(sqlStatement),
            SqlStatement = sqlStatement,
            ExecutedAt = DateTime.Now
        };
    }

    /// <summary>
    /// 建立成功的查詢結果（包含交易資訊）
    /// </summary>
    /// <param name="data">查詢資料</param>
    /// <param name="executionTime">執行時間</param>
    /// <param name="sqlStatement">SQL 語句</param>
    /// <param name="transactionStarted">是否啟動了交易</param>
    /// <param name="transactionCommitted">是否提交了交易</param>
    /// <param name="transactionRolledBack">是否回復了交易</param>
    /// <param name="transactionState">交易狀態</param>
    /// <returns>成功的查詢結果</returns>
    public static QueryResult Success(DataTable? data, TimeSpan executionTime, string sqlStatement,
        bool transactionStarted = false, bool transactionCommitted = false, bool transactionRolledBack = false,
        TransactionState transactionState = TransactionState.None)
    {
        return new QueryResult
        {
            Data = data,
            RowsAffected = data?.Rows.Count ?? 0,
            ExecutionTime = executionTime,
            IsSuccess = true,
            QueryType = DetermineQueryType(sqlStatement),
            SqlStatement = sqlStatement,
            ExecutedAt = DateTime.Now,
            TransactionStarted = transactionStarted,
            TransactionCommitted = transactionCommitted,
            TransactionRolledBack = transactionRolledBack,
            TransactionState = transactionState
        };
    }

    /// <summary>
    /// 建立成功的分頁查詢結果
    /// </summary>
    /// <param name="data">查詢資料</param>
    /// <param name="executionTime">執行時間</param>
    /// <param name="selectStatement">原始 SELECT 語句</param>
    /// <param name="pageSize">每頁資料筆數</param>
    /// <param name="currentPage">當前頁碼</param>
    /// <param name="totalRows">總資料筆數</param>
    /// <param name="parameters">查詢參數</param>
    /// <returns>成功的分頁查詢結果</returns>
    public static QueryResult SuccessWithPagination(DataTable? data, TimeSpan executionTime, string selectStatement,
        int pageSize, int currentPage, long totalRows, Dictionary<string, object>? parameters = null)
    {
        return new QueryResult
        {
            Data = data,
            RowsAffected = data?.Rows.Count ?? 0,
            ExecutionTime = executionTime,
            IsSuccess = true,
            QueryType = QueryType.Select,
            SqlStatement = selectStatement,
            ExecutedAt = DateTime.Now,
            SelectStatement = selectStatement,
            Parameters = parameters,
            PageSize = pageSize,
            CurrentPage = currentPage,
            TotalRows = totalRows,
            HasMoreData = (currentPage * pageSize) < totalRows,
            IsPagedResult = true
        };
    }

    /// <summary>
    /// 建立成功的非查詢結果（包含交易資訊）
    /// </summary>
    /// <param name="rowsAffected">受影響的資料列數</param>
    /// <param name="executionTime">執行時間</param>
    /// <param name="sqlStatement">SQL 語句</param>
    /// <param name="transactionStarted">是否啟動了交易</param>
    /// <param name="transactionCommitted">是否提交了交易</param>
    /// <param name="transactionRolledBack">是否回復了交易</param>
    /// <param name="transactionState">交易狀態</param>
    /// <returns>成功的非查詢結果</returns>
    public static QueryResult Success(int rowsAffected, TimeSpan executionTime, string sqlStatement,
        bool transactionStarted = false, bool transactionCommitted = false, bool transactionRolledBack = false,
        TransactionState transactionState = TransactionState.None)
    {
        return new QueryResult
        {
            Data = null,
            RowsAffected = rowsAffected,
            ExecutionTime = executionTime,
            IsSuccess = true,
            QueryType = DetermineQueryType(sqlStatement),
            SqlStatement = sqlStatement,
            ExecutedAt = DateTime.Now,
            TransactionStarted = transactionStarted,
            TransactionCommitted = transactionCommitted,
            TransactionRolledBack = transactionRolledBack,
            TransactionState = transactionState
        };
    }

    /// <summary>
    /// 建立失敗的查詢結果
    /// </summary>
    /// <param name="errorMessage">錯誤訊息</param>
    /// <param name="exception">例外物件</param>
    /// <param name="sqlStatement">SQL 語句</param>
    /// <param name="executionTime">執行時間</param>
    /// <returns>失敗的查詢結果</returns>
    public static QueryResult Failure(string errorMessage, Exception? exception, string sqlStatement, TimeSpan executionTime = default)
    {
        return new QueryResult
        {
            Data = null,
            RowsAffected = 0,
            ExecutionTime = executionTime,
            ErrorMessage = errorMessage,
            IsSuccess = false,
            QueryType = DetermineQueryType(sqlStatement),
            SqlStatement = sqlStatement,
            Exception = exception,
            ExecutedAt = DateTime.Now
        };
    }

    /// <summary>
    /// 根據 SQL 語句判斷查詢類型
    /// </summary>
    /// <param name="sqlStatement">SQL 語句</param>
    /// <returns>查詢類型</returns>
    private static QueryType DetermineQueryType(string sqlStatement)
    {
        if (string.IsNullOrWhiteSpace(sqlStatement))
            return QueryType.Unknown;

        var trimmedSql = sqlStatement.Trim().ToUpperInvariant();
        
        return trimmedSql switch
        {
            var sql when sql.StartsWith("SELECT") => QueryType.Select,
            var sql when sql.StartsWith("INSERT") => QueryType.Insert,
            var sql when sql.StartsWith("UPDATE") => QueryType.Update,
            var sql when sql.StartsWith("DELETE") => QueryType.Delete,
            var sql when sql.StartsWith("CREATE") => QueryType.Create,
            var sql when sql.StartsWith("ALTER") => QueryType.Alter,
            var sql when sql.StartsWith("DROP") => QueryType.Drop,
            var sql when sql.StartsWith("TRUNCATE") => QueryType.Truncate,
            var sql when sql.StartsWith("EXEC") || sql.StartsWith("CALL") => QueryType.Execute,
            _ => QueryType.Other
        };
    }

    /// <summary>
    /// 取得格式化的執行結果摘要
    /// </summary>
    /// <returns>執行結果摘要</returns>
    public string GetSummary()
    {
        if (!IsSuccess)
            return $"執行失敗：{ErrorMessage}";

        return QueryType switch
        {
            QueryType.Select when IsPagedResult =>
                $"查詢完成，第 {CurrentPage} 頁，顯示 {RowsAffected} 筆記錄（共 {TotalRows} 筆），耗時 {ExecutionTime.TotalMilliseconds:F0} 毫秒",
            QueryType.Select =>
                $"查詢完成，返回 {RowsAffected} 筆記錄，耗時 {ExecutionTime.TotalMilliseconds:F0} 毫秒",
            QueryType.Insert =>
                $"插入完成，影響 {RowsAffected} 筆記錄，耗時 {ExecutionTime.TotalMilliseconds:F0} 毫秒",
            QueryType.Update =>
                $"更新完成，影響 {RowsAffected} 筆記錄，耗時 {ExecutionTime.TotalMilliseconds:F0} 毫秒",
            QueryType.Delete =>
                $"刪除完成，影響 {RowsAffected} 筆記錄，耗時 {ExecutionTime.TotalMilliseconds:F0} 毫秒",
            _ => $"執行完成，耗時 {ExecutionTime.TotalMilliseconds:F0} 毫秒"
        };
    }
}

/// <summary>
/// 查詢類型列舉
/// </summary>
public enum QueryType
{
    Unknown,
    Select,
    Insert,
    Update,
    Delete,
    Create,
    Alter,
    Drop,
    Truncate,
    Execute,
    Other
}